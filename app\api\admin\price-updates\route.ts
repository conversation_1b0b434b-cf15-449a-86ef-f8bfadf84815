import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { spawn } from "child_process";
import path from "path";

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { dryRun = true, batchSize = 50 } = body;

    // Validate batch size
    if (batchSize < 1 || batchSize > 100) {
      return NextResponse.json(
        { error: 'Batch size must be between 1 and 100' },
        { status: 400 }
      );
    }

    // Build command arguments
    const args = [
      'scripts/update-product-prices.ts',
      `--batch-size=${batchSize}`
    ];

    if (dryRun) {
      args.push('--dry-run');
    }

    // Start the price update process in the background
    const scriptPath = path.join(process.cwd(), 'scripts/update-product-prices.ts');
    
    try {
      const child = spawn('npx', ['ts-node', scriptPath, ...args.slice(1)], {
        detached: true,
        stdio: 'ignore',
        cwd: process.cwd(),
        env: { ...process.env }
      });

      // Detach the process so it continues running
      child.unref();

      return NextResponse.json({
        success: true,
        message: `Price update process started successfully${dryRun ? ' (dry run mode)' : ''}`,
        processId: child.pid,
        batchSize,
        dryRun,
        priceRules: {
          range1000to1400: '+M250',
          otherProducts: '+M300'
        }
      });

    } catch (spawnError) {
      console.error('Error spawning price update process:', spawnError);
      return NextResponse.json(
        { error: 'Failed to start price update process' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Price update API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Return price update information and rules
    return NextResponse.json({
      success: true,
      priceUpdateRules: {
        description: 'Systematic price increase across all products',
        rules: [
          {
            condition: 'Products between M1000 and M1400',
            increase: 'M250',
            example: 'M1200 → M1450'
          },
          {
            condition: 'All other products (below M1000 or above M1400)',
            increase: 'M300',
            example: 'M800 → M1100, M1500 → M1800'
          }
        ],
        notes: [
          'Discounted prices will be adjusted proportionally',
          'Only active products will be updated',
          'Changes are applied in batches to prevent system overload'
        ]
      }
    });

  } catch (error) {
    console.error('Price update info API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
