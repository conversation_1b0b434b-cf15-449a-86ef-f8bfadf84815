import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { ApiResponse } from "@/utils/types";

// GET /api/admin/cost-prices - Get products needing cost prices
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    // Get products with their order information
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        brand: true,
        price: true,
        costPrice: true,
        shippingFee: true,
        lateCollectionFee: true,
        totalCost: true,
        costPriceUpdatedAt: true,
        costPriceUpdatedBy: true,
        feesUpdatedAt: true,
        feesUpdatedBy: true,
        images: true,
        orderItems: {
          select: {
            id: true,
            quantity: true,
            price: true,
            order: {
              select: {
                id: true,
                orderNumber: true,
                createdAt: true,
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      where: {
        isActive: true,
        orderItems: {
          some: {}, // Only products that have been ordered
        },
      },
      orderBy: [
        { costPrice: 'asc' }, // Products without cost price first (null values come first)
        { createdAt: 'desc' },
      ],
    });

    // Calculate statistics and format data
    const productsWithStats = products.map(product => {
      const orderCount = product.orderItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalRevenue = product.orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const potentialProfit = product.totalCost
        ? product.orderItems.reduce((sum, item) => sum + ((item.price - product.totalCost!) * item.quantity), 0)
        : null;

      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        price: product.price,
        costPrice: product.costPrice,
        shippingFee: product.shippingFee,
        lateCollectionFee: product.lateCollectionFee,
        totalCost: product.totalCost,
        costPriceUpdatedAt: product.costPriceUpdatedAt,
        costPriceUpdatedBy: product.costPriceUpdatedBy,
        feesUpdatedAt: product.feesUpdatedAt,
        feesUpdatedBy: product.feesUpdatedBy,
        images: product.images,
        orderCount,
        totalRevenue,
        potentialProfit,
      };
    });

    // Calculate overall statistics
    const stats = {
      totalProducts: products.length,
      productsWithCostPrice: products.filter(p => p.costPrice !== null).length,
      pendingCostPrices: products.filter(p => p.costPrice === null).length,
      totalPendingRevenue: productsWithStats
        .filter(p => p.costPrice === null)
        .reduce((sum, p) => sum + p.totalRevenue, 0),
    };

    // Filter to show products without cost price first, then products with cost price
    const prioritizedProducts = [
      ...productsWithStats.filter(p => p.costPrice === null),
      ...productsWithStats.filter(p => p.costPrice !== null),
    ];

    const response: ApiResponse<{ products: typeof prioritizedProducts; stats: typeof stats }> = {
      success: true,
      data: {
        products: prioritizedProducts,
        stats,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching cost prices:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch cost prices",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/cost-prices - Update product cost price
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { productId, costPrice, shippingFee, lateCollectionFee } = body;

    if (!productId || !costPrice || costPrice <= 0) {
      return NextResponse.json(
        { success: false, error: "Valid product ID and cost price required" },
        { status: 400 }
      );
    }

    if (shippingFee !== undefined && (shippingFee < 0)) {
      return NextResponse.json(
        { success: false, error: "Shipping fee must be non-negative" },
        { status: 400 }
      );
    }

    if (lateCollectionFee !== undefined && lateCollectionFee !== null && (lateCollectionFee < 0)) {
      return NextResponse.json(
        { success: false, error: "Late collection fee must be non-negative" },
        { status: 400 }
      );
    }

    // Calculate total cost
    const parsedCostPrice = parseFloat(costPrice);
    const parsedShippingFee = shippingFee !== undefined ? parseFloat(shippingFee) : 0;
    const parsedLateCollectionFee = lateCollectionFee !== undefined && lateCollectionFee !== null ? parseFloat(lateCollectionFee) : 0;
    const totalCost = parsedCostPrice + parsedShippingFee + parsedLateCollectionFee;

    // Update the product cost price and fees
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: {
        costPrice: parsedCostPrice,
        shippingFee: shippingFee !== undefined ? parsedShippingFee : undefined,
        lateCollectionFee: lateCollectionFee !== undefined ? (lateCollectionFee !== null ? parsedLateCollectionFee : null) : undefined,
        totalCost: totalCost,
        costPriceUpdatedAt: new Date(),
        costPriceUpdatedBy: user.id,
        feesUpdatedAt: new Date(),
        feesUpdatedBy: user.id,
      },
    });

    // Update all existing order items for this product with the cost details and calculate profit
    await prisma.orderItem.updateMany({
      where: { productId: productId },
      data: {
        costPrice: parsedCostPrice,
        shippingFee: shippingFee !== undefined ? parsedShippingFee : undefined,
        lateCollectionFee: lateCollectionFee !== undefined ? (lateCollectionFee !== null ? parsedLateCollectionFee : null) : undefined,
        totalCost: totalCost,
      },
    });

    // Calculate profit for each order item using total cost
    const orderItems = await prisma.orderItem.findMany({
      where: { productId: productId },
    });

    for (const item of orderItems) {
      const profit = (item.price - totalCost) * item.quantity;
      await prisma.orderItem.update({
        where: { id: item.id },
        data: { profit },
      });
    }

    // Update order totals for orders containing this product
    const ordersToUpdate = await prisma.order.findMany({
      where: {
        orderItems: {
          some: { productId: productId },
        },
      },
      include: {
        orderItems: true,
      },
    });

    for (const order of ordersToUpdate) {
      const totalCostPrice = order.orderItems.reduce((sum, item) => {
        return sum + (item.totalCost ? item.totalCost * item.quantity : 0);
      }, 0);

      const totalProfit = order.orderItems.reduce((sum, item) => {
        return sum + (item.profit || 0);
      }, 0);

      // Only update if all items have total costs
      const allItemsHaveTotalCost = order.orderItems.every(item => item.totalCost !== null);

      await prisma.order.update({
        where: { id: order.id },
        data: {
          totalCostPrice: allItemsHaveTotalCost ? totalCostPrice : null,
          totalProfit: allItemsHaveTotalCost ? totalProfit : null,
        },
      });
    }

    const response: ApiResponse<typeof updatedProduct> = {
      success: true,
      data: updatedProduct,
      message: "Cost price updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating cost price:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update cost price" },
      { status: 500 }
    );
  }
}
