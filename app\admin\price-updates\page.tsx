"use client";

import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import PriceUpdateManager from "@/components/admin/price-updates/price-update-manager";
import { User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

export default function AdminPriceUpdatesPage() {
  return (
    <AdminRoute>
      <AdminPriceUpdatesPageContent />
    </AdminRoute>
  );
}

function AdminPriceUpdatesPageContent() {
  const { data: session, isPending } = useSession();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      if (session?.user?.id) {
        try {
          const userData = await getUserById(session.user.id);
          if (userData.success && userData.data) {
            setUser(userData.data);
          }
        } catch (error) {
          console.error("Error fetching user:", error);
        }
      }
      setLoading(false);
    };

    if (!isPending) {
      fetchUser();
    }
  }, [session, isPending]);

  if (isPending || loading) {
    return <SpinnerCircle4 />;
  }

  if (!user) {
    return <div>Error loading user data</div>;
  }

  // Type cast to include the role property that exists at runtime
  const userWithRole = user as User & { role: 'ADMIN' };

  return (
    <AdminLayout user={userWithRole}>
      <PriceUpdateManager />
    </AdminLayout>
  );
}
