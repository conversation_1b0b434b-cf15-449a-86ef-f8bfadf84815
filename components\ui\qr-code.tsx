'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Download, RefreshCw } from 'lucide-react';
import { generateQRCode, downloadQRCode, QRCodeOptions } from '@/lib/qr-code-utils';

interface QRCodeProps {
  data: string;
  size?: number;
  options?: QRCodeOptions;
  showDownload?: boolean;
  showRefresh?: boolean;
  className?: string;
  alt?: string;
}

export default function QRCode({
  data,
  size = 256,
  options = {},
  showDownload = true,
  showRefresh = false,
  className = '',
  alt = 'QR Code'
}: QRCodeProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  const generateQR = async () => {
    try {
      setIsLoading(true);
      setError('');
      const qrOptions = {
        width: size,
        height: size,
        ...options
      };
      const url = await generateQRCode(data, qrOptions);
      setQrCodeUrl(url);
    } catch (err) {
      setError('Failed to generate QR code');
      console.error('QR code generation error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      const filename = `qr-code-${Date.now()}.png`;
      await downloadQRCode(data, filename, {
        width: size,
        height: size,
        ...options
      });
    } catch (err) {
      console.error('Download error:', err);
    }
  };

  const handleRefresh = () => {
    generateQR();
  };

  useEffect(() => {
    if (data) {
      generateQR();
    }
  }, [data, size, options]);

  if (!data) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`} style={{ width: size, height: size }}>
        <p className="text-gray-500 text-sm">No data provided</p>
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center gap-4 ${className}`}>
      <div className="relative">
        {isLoading ? (
          <div 
            className="flex items-center justify-center bg-gray-100 rounded-lg animate-pulse"
            style={{ width: size, height: size }}
          >
            <RefreshCw className="h-8 w-8 text-gray-400 animate-spin" />
          </div>
        ) : error ? (
          <div 
            className="flex items-center justify-center bg-red-50 border border-red-200 rounded-lg"
            style={{ width: size, height: size }}
          >
            <p className="text-red-500 text-sm text-center px-4">{error}</p>
          </div>
        ) : (
          <img
            src={qrCodeUrl}
            alt={alt}
            className="rounded-lg border border-gray-200"
            style={{ width: size, height: size }}
          />
        )}
      </div>
      
      <div className="flex gap-2">
        {showDownload && !isLoading && !error && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleDownload}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download
          </Button>
        )}
        {showRefresh && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        )}
      </div>
    </div>
  );
}

// Specialized component for partner QR codes
interface PartnerQRCodeProps {
  referralCode: string;
  discountCode: string;
  discountAmount: number;
  partnerName: string;
  size?: number;
  showDownload?: boolean;
  showRefresh?: boolean;
  className?: string;
}

export function PartnerQRCode({
  referralCode,
  discountCode,
  discountAmount,
  partnerName,
  size = 256,
  showDownload = true,
  showRefresh = false,
  className = ''
}: PartnerQRCodeProps) {
  const url = new URL('https://rivvsneakers.shop');
  url.searchParams.set('ref', referralCode);
  url.searchParams.set('discount', discountCode);
  
  const qrData = url.toString();
  const alt = `QR Code for ${partnerName} - ${referralCode}`;

  return (
    <div className={`flex flex-col items-center gap-4 ${className}`}>
      <QRCode
        data={qrData}
        size={size}
        options={{
          color: { dark: '#000000', light: '#FFFFFF' }, // Pure black/white for maximum contrast
          errorCorrectionLevel: 'H', // High error correction for better scanning
          margin: 4, // Increased margin for better scanning
          width: Math.max(size, 512), // Ensure minimum size for readability
          height: Math.max(size, 512) // Ensure minimum size for readability
        }}
        showDownload={showDownload}
        showRefresh={showRefresh}
        alt={alt}
      />
      
      <div className="text-center text-sm text-gray-600 max-w-xs">
        <p><strong>Referral Code:</strong> {referralCode}</p>
        <p><strong>Discount Code:</strong> {discountCode}</p>
        <p><strong>Discount Amount:</strong> M{discountAmount.toFixed(2)}</p>
      </div>
    </div>
  );
} 