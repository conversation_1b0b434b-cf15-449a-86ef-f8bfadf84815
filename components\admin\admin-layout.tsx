"use client";

import { useState } from "react";
import { User } from "@/utils/types";
import AdminSidebar from "./admin-sidebar";
import AdminHeader from "./admin-header";

interface AdminLayoutProps {
  user: User;
  children: React.ReactNode;
}

export default function AdminLayout({ user, children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminHeader user={user} onMenuClick={() => setSidebarOpen(true)} />
      <div className="flex flex-col lg:flex-row">
        {/* Sidebar for desktop, drawer for mobile */}
        <AdminSidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
        <main className="flex-1 p-2 sm:p-4 lg:p-6 w-full min-h-screen bg-white">
          {children}
        </main>
      </div>
    </div>
  );
}
