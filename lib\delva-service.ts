import { BUSINESS_CONFIG, DELIVERY_PARTNER } from '@/utils/constants';
import { sendEmail } from './email';
import prisma from './prisma';

/**
 * Delva Delivery Service
 * Handles all interactions with Delva delivery partner
 * Automates delivery scheduling, tracking, and notifications
 */

export interface DelvaDeliveryRequest {
  orderId: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  deliveryAddress: string;
  items: Array<{
    name: string;
    quantity: number;
    size?: string;
    color?: string;
  }>;
  totalAmount: number;
  deliveryFee: number;
  isBulkDelivery: boolean;
  preferredDeliveryDate?: Date;
  specialInstructions?: string;
}

export interface DelvaDeliveryResponse {
  success: boolean;
  trackingNumber?: string;
  estimatedDeliveryDate?: Date;
  message?: string;
  error?: string;
}

export interface DelvaDeliveryUpdate {
  trackingNumber: string;
  status: 'PICKED_UP' | 'IN_TRANSIT' | 'OUT_FOR_DELIVERY' | 'DELIVERED' | 'FAILED' | 'RETURNED';
  location?: string;
  estimatedDeliveryTime?: string;
  notes?: string;
  timestamp: Date;
}

/**
 * Schedule delivery with Delva
 * Automatically sends delivery request and handles confirmation
 */
export async function scheduleDelvaDelivery(deliveryData: DelvaDeliveryRequest): Promise<DelvaDeliveryResponse> {
  try {
    // Create delivery record in database
    const deliveryRecord = await prisma.deliveryRecord.create({
      data: {
        orderId: deliveryData.orderId,
        partner: DELIVERY_PARTNER.DELVA,
        status: 'SCHEDULED',
        customerName: deliveryData.customerName,
        customerPhone: deliveryData.customerPhone,
        customerEmail: deliveryData.customerEmail,
        deliveryAddress: deliveryData.deliveryAddress,
        items: deliveryData.items,
        totalAmount: deliveryData.totalAmount,
        deliveryFee: deliveryData.deliveryFee,
        isBulkDelivery: deliveryData.isBulkDelivery,
        preferredDeliveryDate: deliveryData.preferredDeliveryDate,
        specialInstructions: deliveryData.specialInstructions,
        scheduledAt: new Date(),
      },
    });

    // Send delivery request to Delva
    const delvaResponse = await sendDelvaDeliveryRequest(deliveryData);

    if (delvaResponse.success) {
      // Update delivery record with tracking info
      await prisma.deliveryRecord.update({
        where: { id: deliveryRecord.id },
        data: {
          trackingNumber: delvaResponse.trackingNumber,
          estimatedDeliveryDate: delvaResponse.estimatedDeliveryDate,
          status: 'CONFIRMED',
        },
      });

      // Send confirmation email to customer
      await sendDelvaDeliveryConfirmationEmail(deliveryData, delvaResponse);

      // Send notification to admin
      await sendDelvaDeliveryAdminNotification(deliveryData, delvaResponse);

      return delvaResponse;
    } else {
      // Update delivery record with error
      await prisma.deliveryRecord.update({
        where: { id: deliveryRecord.id },
        data: {
          status: 'FAILED',
          notes: delvaResponse.error,
        },
      });

      return delvaResponse;
    }
  } catch (error) {
    console.error('Error scheduling Delva delivery:', error);
    return {
      success: false,
      error: 'Failed to schedule delivery. Please try again.',
    };
  }
}

/**
 * Send delivery request to Delva API
 * This would integrate with Delva's actual API
 */
async function sendDelvaDeliveryRequest(deliveryData: DelvaDeliveryRequest): Promise<DelvaDeliveryResponse> {
  try {
    // TODO: Replace with actual Delva API integration
    // For now, simulate API call
    const response = await fetch(`${process.env.DELVA_API_URL}/deliveries`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.DELVA_API_KEY}`,
      },
      body: JSON.stringify({
        order_number: deliveryData.orderNumber,
        customer_name: deliveryData.customerName,
        customer_phone: deliveryData.customerPhone,
        customer_email: deliveryData.customerEmail,
        delivery_address: deliveryData.deliveryAddress,
        items: deliveryData.items,
        total_amount: deliveryData.totalAmount,
        delivery_fee: deliveryData.deliveryFee,
        is_bulk_delivery: deliveryData.isBulkDelivery,
        preferred_delivery_date: deliveryData.preferredDeliveryDate?.toISOString(),
        special_instructions: deliveryData.specialInstructions,
      }),
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        trackingNumber: data.tracking_number,
        estimatedDeliveryDate: new Date(data.estimated_delivery_date),
        message: 'Delivery scheduled successfully',
      };
    } else {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.message || 'Failed to schedule delivery with Delva',
      };
    }
  } catch (error) {
    console.error('Delva API error:', error);
    return {
      success: false,
      error: 'Unable to connect to Delva delivery service',
    };
  }
}

/**
 * Send delivery confirmation email to customer
 */
async function sendDelvaDeliveryConfirmationEmail(
  deliveryData: DelvaDeliveryRequest,
  delvaResponse: DelvaDeliveryResponse
) {
  const emailData = {
    customerName: deliveryData.customerName,
    orderNumber: deliveryData.orderNumber,
    trackingNumber: delvaResponse.trackingNumber,
    estimatedDeliveryDate: delvaResponse.estimatedDeliveryDate?.toLocaleDateString('en-LS', {
      timeZone: 'Africa/Maseru',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }),
    deliveryAddress: deliveryData.deliveryAddress,
    items: deliveryData.items,
    totalAmount: deliveryData.totalAmount,
    deliveryFee: deliveryData.deliveryFee,
    delvaContact: BUSINESS_CONFIG.DELVA_CONTACT,
  };

  const template = {
    subject: `🚚 Delivery Scheduled - Order ${deliveryData.orderNumber} | ${BUSINESS_CONFIG.COMPANY_NAME}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">🚚 Delivery Scheduled Successfully!</h2>
        
        <p>Dear ${emailData.customerName},</p>
        
        <p>Great news! Your order <strong>${emailData.orderNumber}</strong> has been scheduled for delivery with our trusted partner <strong>Delva</strong>.</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📦 Delivery Details</h3>
          <p><strong>Tracking Number:</strong> ${emailData.trackingNumber}</p>
          <p><strong>Estimated Delivery:</strong> ${emailData.estimatedDeliveryDate}</p>
          <p><strong>Delivery Address:</strong> ${emailData.deliveryAddress}</p>
          <p><strong>Delivery Fee:</strong> M${emailData.deliveryFee}</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📞 Need Help?</h3>
          <p>If you have any questions about your delivery:</p>
          <ul>
            <li><strong>Delva Support:</strong> ${emailData.delvaContact.PHONE}</li>
            <li><strong>WhatsApp:</strong> ${emailData.delvaContact.WHATSAPP}</li>
            <li><strong>Email:</strong> ${emailData.delvaContact.EMAIL}</li>
          </ul>
        </div>
        
        <p>Thank you for choosing ${BUSINESS_CONFIG.COMPANY_NAME}!</p>
      </div>
    `,
    text: `
      Delivery Scheduled Successfully!
      
      Dear ${emailData.customerName},
      
      Your order ${emailData.orderNumber} has been scheduled for delivery with Delva.
      
      Tracking Number: ${emailData.trackingNumber}
      Estimated Delivery: ${emailData.estimatedDeliveryDate}
      Delivery Address: ${emailData.deliveryAddress}
      
      For delivery support, contact Delva:
      Phone: ${emailData.delvaContact.PHONE}
      WhatsApp: ${emailData.delvaContact.WHATSAPP}
      Email: ${emailData.delvaContact.EMAIL}
      
      Thank you for choosing ${BUSINESS_CONFIG.COMPANY_NAME}!
    `,
  };

  await sendEmail({
    to: deliveryData.customerEmail,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

/**
 * Send notification to admin about scheduled delivery
 */
async function sendDelvaDeliveryAdminNotification(
  deliveryData: DelvaDeliveryRequest,
  delvaResponse: DelvaDeliveryResponse
) {
  const template = {
    subject: `📦 Delva Delivery Scheduled - Order ${deliveryData.orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">📦 Delva Delivery Scheduled</h2>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Order Details</h3>
          <p><strong>Order Number:</strong> ${deliveryData.orderNumber}</p>
          <p><strong>Customer:</strong> ${deliveryData.customerName}</p>
          <p><strong>Phone:</strong> ${deliveryData.customerPhone}</p>
          <p><strong>Email:</strong> ${deliveryData.customerEmail}</p>
          <p><strong>Address:</strong> ${deliveryData.deliveryAddress}</p>
          <p><strong>Total Amount:</strong> M${deliveryData.totalAmount}</p>
          <p><strong>Delivery Fee:</strong> M${deliveryData.deliveryFee}</p>
          <p><strong>Bulk Delivery:</strong> ${deliveryData.isBulkDelivery ? 'Yes' : 'No'}</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Delivery Information</h3>
          <p><strong>Tracking Number:</strong> ${delvaResponse.trackingNumber}</p>
          <p><strong>Estimated Delivery:</strong> ${delvaResponse.estimatedDeliveryDate?.toLocaleDateString()}</p>
          <p><strong>Status:</strong> Confirmed</p>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Items for Delivery</h3>
          <ul>
            ${deliveryData.items.map(item => 
              `<li>${item.name} - Qty: ${item.quantity}${item.size ? ` - Size: ${item.size}` : ''}${item.color ? ` - Color: ${item.color}` : ''}</li>`
            ).join('')}
          </ul>
        </div>
        
        ${deliveryData.specialInstructions ? `
          <div style="background: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Special Instructions</h3>
            <p>${deliveryData.specialInstructions}</p>
          </div>
        ` : ''}
      </div>
    `,
    text: `
      Delva Delivery Scheduled
      
      Order Number: ${deliveryData.orderNumber}
      Customer: ${deliveryData.customerName}
      Phone: ${deliveryData.customerPhone}
      Address: ${deliveryData.deliveryAddress}
      Total Amount: M${deliveryData.totalAmount}
      Delivery Fee: M${deliveryData.deliveryFee}
      
      Tracking Number: ${delvaResponse.trackingNumber}
      Estimated Delivery: ${delvaResponse.estimatedDeliveryDate?.toLocaleDateString()}
      
      Items:
      ${deliveryData.items.map(item => 
        `- ${item.name} - Qty: ${item.quantity}${item.size ? ` - Size: ${item.size}` : ''}${item.color ? ` - Color: ${item.color}` : ''}`
      ).join('\n')}
      
      ${deliveryData.specialInstructions ? `Special Instructions: ${deliveryData.specialInstructions}` : ''}
    `,
  };

  await sendEmail({
    to: BUSINESS_CONFIG.SUPPORT_EMAIL,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

/**
 * Update delivery status from Delva webhook
 * This would be called by Delva when delivery status changes
 */
export async function updateDelvaDeliveryStatus(update: DelvaDeliveryUpdate): Promise<boolean> {
  try {
    // Find delivery record by tracking number
    const deliveryRecord = await prisma.deliveryRecord.findFirst({
      where: { trackingNumber: update.trackingNumber },
      include: { order: true },
    });

    if (!deliveryRecord) {
      console.error('Delivery record not found for tracking number:', update.trackingNumber);
      return false;
    }

    // Update delivery record
    await prisma.deliveryRecord.update({
      where: { id: deliveryRecord.id },
      data: {
        status: update.status,
        lastUpdate: update.timestamp,
        notes: update.notes,
      },
    });

    // Update order status if delivered
    if (update.status === 'DELIVERED') {
      await prisma.order.update({
        where: { id: deliveryRecord.orderId },
        data: {
          status: 'DELIVERED',
          deliveredAt: update.timestamp,
        },
      });

      // Send delivery confirmation to customer
      await sendDeliveryCompletionEmail(deliveryRecord);
    }

    // Send status update to customer
    await sendDeliveryStatusUpdateEmail(deliveryRecord, update);

    return true;
  } catch (error) {
    console.error('Error updating delivery status:', error);
    return false;
  }
}

/**
 * Send delivery completion email to customer
 */
async function sendDeliveryCompletionEmail(deliveryRecord: any) {
  const template = {
    subject: `🎉 Delivery Complete - Order ${deliveryRecord.order.orderNumber} | ${BUSINESS_CONFIG.COMPANY_NAME}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #10b981;">🎉 Delivery Complete!</h2>
        
        <p>Dear ${deliveryRecord.customerName},</p>
        
        <p>Great news! Your order <strong>${deliveryRecord.order.orderNumber}</strong> has been successfully delivered.</p>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">✅ Delivery Confirmed</h3>
          <p><strong>Order Number:</strong> ${deliveryRecord.order.orderNumber}</p>
          <p><strong>Tracking Number:</strong> ${deliveryRecord.trackingNumber}</p>
          <p><strong>Delivered At:</strong> ${new Date().toLocaleDateString('en-LS', {
            timeZone: 'Africa/Maseru',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          })}</p>
        </div>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📝 What's Next?</h3>
          <p>We hope you love your new shoes! Here's what you can do next:</p>
          <ul>
            <li>Try on your shoes and make sure they fit perfectly</li>
            <li>Share your experience with us on social media</li>
            <li>Consider leaving a review to help other customers</li>
            <li>Shop for more amazing styles on our website</li>
          </ul>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">💬 Need Help?</h3>
          <p>If you have any questions or concerns:</p>
          <ul>
            <li><strong>WhatsApp:</strong> ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}</li>
            <li><strong>Email:</strong> ${BUSINESS_CONFIG.SUPPORT_EMAIL}</li>
            <li><strong>Phone:</strong> ${BUSINESS_CONFIG.SUPPORT_PHONE}</li>
          </ul>
        </div>
        
        <p>Thank you for choosing ${BUSINESS_CONFIG.COMPANY_NAME}!</p>
      </div>
    `,
    text: `
      Delivery Complete!
      
      Dear ${deliveryRecord.customerName},
      
      Your order ${deliveryRecord.order.orderNumber} has been successfully delivered.
      
      Order Number: ${deliveryRecord.order.orderNumber}
      Tracking Number: ${deliveryRecord.trackingNumber}
      Delivered At: ${new Date().toLocaleDateString()}
      
      We hope you love your new shoes!
      
      For support:
      WhatsApp: ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}
      Email: ${BUSINESS_CONFIG.SUPPORT_EMAIL}
      Phone: ${BUSINESS_CONFIG.SUPPORT_PHONE}
      
      Thank you for choosing ${BUSINESS_CONFIG.COMPANY_NAME}!
    `,
  };

  await sendEmail({
    to: deliveryRecord.customerEmail,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

/**
 * Send delivery status update email to customer
 */
async function sendDeliveryStatusUpdateEmail(deliveryRecord: any, update: DelvaDeliveryUpdate) {
  const statusMessages = {
    'PICKED_UP': 'Your order has been picked up and is on its way!',
    'IN_TRANSIT': 'Your order is in transit and heading to your location.',
    'OUT_FOR_DELIVERY': 'Your order is out for delivery and will arrive soon!',
    'DELIVERED': 'Your order has been successfully delivered!',
    'FAILED': 'Delivery was attempted but could not be completed.',
    'RETURNED': 'Your order has been returned to our facility.',
  };

  const template = {
    subject: `📦 Delivery Update - Order ${deliveryRecord.order.orderNumber} | ${BUSINESS_CONFIG.COMPANY_NAME}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">📦 Delivery Status Update</h2>
        
        <p>Dear ${deliveryRecord.customerName},</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📋 Update Details</h3>
          <p><strong>Order Number:</strong> ${deliveryRecord.order.orderNumber}</p>
          <p><strong>Tracking Number:</strong> ${deliveryRecord.trackingNumber}</p>
          <p><strong>Status:</strong> ${update.status.replace('_', ' ')}</p>
          <p><strong>Updated At:</strong> ${update.timestamp.toLocaleDateString('en-LS', {
            timeZone: 'Africa/Maseru',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          })}</p>
          ${update.location ? `<p><strong>Location:</strong> ${update.location}</p>` : ''}
          ${update.estimatedDeliveryTime ? `<p><strong>Estimated Delivery:</strong> ${update.estimatedDeliveryTime}</p>` : ''}
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">ℹ️ Status Information</h3>
          <p>${statusMessages[update.status as keyof typeof statusMessages]}</p>
          ${update.notes ? `<p><strong>Notes:</strong> ${update.notes}</p>` : ''}
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">💬 Need Help?</h3>
          <p>If you have any questions about your delivery:</p>
          <ul>
            <li><strong>Delva Support:</strong> ${BUSINESS_CONFIG.DELVA_CONTACT.PHONE}</li>
            <li><strong>WhatsApp:</strong> ${BUSINESS_CONFIG.DELVA_CONTACT.WHATSAPP}</li>
            <li><strong>Our Support:</strong> ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}</li>
          </ul>
        </div>
      </div>
    `,
    text: `
      Delivery Status Update
      
      Dear ${deliveryRecord.customerName},
      
      Your order ${deliveryRecord.order.orderNumber} status has been updated.
      
      Order Number: ${deliveryRecord.order.orderNumber}
      Tracking Number: ${deliveryRecord.trackingNumber}
      Status: ${update.status.replace('_', ' ')}
      Updated At: ${update.timestamp.toLocaleDateString()}
      ${update.location ? `Location: ${update.location}` : ''}
      ${update.estimatedDeliveryTime ? `Estimated Delivery: ${update.estimatedDeliveryTime}` : ''}
      
      ${statusMessages[update.status as keyof typeof statusMessages]}
      ${update.notes ? `Notes: ${update.notes}` : ''}
      
      For delivery support:
      Delva: ${BUSINESS_CONFIG.DELVA_CONTACT.PHONE}
      WhatsApp: ${BUSINESS_CONFIG.DELVA_CONTACT.WHATSAPP}
      Our Support: ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}
    `,
  };

  await sendEmail({
    to: deliveryRecord.customerEmail,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

/**
 * Get delivery statistics for admin dashboard
 */
export async function getDelvaDeliveryStats() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const stats = await prisma.deliveryRecord.groupBy({
    by: ['status'],
    where: {
      partner: DELIVERY_PARTNER.DELVA,
      createdAt: {
        gte: today,
        lt: tomorrow,
      },
    },
    _count: {
      status: true,
    },
  });

  return stats.reduce((acc, stat) => {
    acc[stat.status] = stat._count.status;
    return acc;
  }, {} as Record<string, number>);
}

/**
 * Get pending deliveries for admin review
 */
export async function getPendingDelvaDeliveries() {
  return await prisma.deliveryRecord.findMany({
    where: {
      partner: DELIVERY_PARTNER.DELVA,
      status: {
        in: ['SCHEDULED', 'CONFIRMED', 'PICKED_UP', 'IN_TRANSIT', 'OUT_FOR_DELIVERY'],
      },
    },
    include: {
      order: {
        select: {
          orderNumber: true,
          totalAmount: true,
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
} 