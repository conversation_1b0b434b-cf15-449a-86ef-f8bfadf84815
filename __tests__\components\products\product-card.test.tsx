import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ProductCard from '@/components/products/product-card'
import { Product } from '@/utils/types'

// Mock product data
const mockProduct: Product = {
  id: 'test-product-1',
  name: 'Test Sneaker',
  description: 'A premium test sneaker',
  price: 999.99,
  discountedPrice: 799.99,
  brand: 'TestBrand',
  categoryId: 'test-category',
  images: [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg',
    'https://example.com/image3.jpg'
  ],
  sizes: ['8', '9', '10'],
  colors: ['Black', 'White'],
  stock: 10,
  isActive: true,
  rating: 4.5,
  reviewCount: 25,
  costPrice: null,
  shippingFee: null,
  lateCollectionFee: null,
  totalCost: null,
  costPriceUpdatedAt: null,
  costPriceUpdatedBy: null,
  feesUpdatedAt: null,
  feesUpdatedBy: null,

  createdAt: new Date(),
  updatedAt: new Date(),
  category: {
    id: 'test-category',
    name: 'Test Category',
    description: 'Test category description',
    image: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    products: []
  },
  reviews: [],
  _count: { reviews: 25, orderItems: 0 },
  isFeatured: false
}

describe('ProductCard', () => {
  const mockOnQuickView = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders product information correctly', () => {
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    expect(screen.getByText('Test Sneaker')).toBeInTheDocument()
    expect(screen.getByText('TestBrand')).toBeInTheDocument()
    expect(screen.getByText('M799.99')).toBeInTheDocument()
    expect(screen.getByText('M999.99')).toBeInTheDocument()
    expect(screen.getByText('(25)')).toBeInTheDocument()
  })

  it('displays discount badge when product is on sale', () => {
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    expect(screen.getByText('-20%')).toBeInTheDocument()
  })

  it('shows stock status correctly', () => {
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    expect(screen.getByText('In Stock (10)')).toBeInTheDocument()
  })

  it('displays out of stock when stock is 0', () => {
    const outOfStockProduct = { ...mockProduct, stock: 0 }
    render(<ProductCard product={outOfStockProduct} onQuickView={mockOnQuickView} />)
    
    expect(screen.getByText('Out of Stock')).toBeInTheDocument()
  })

  it('shows available sizes', () => {
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    expect(screen.getByText('8')).toBeInTheDocument()
    expect(screen.getByText('9')).toBeInTheDocument()
    expect(screen.getByText('10')).toBeInTheDocument()
  })

  it('renders rating stars correctly', () => {
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    // Should have 4 full stars and 1 half star for 4.5 rating
    const stars = screen.getAllByTestId(/star/)
    expect(stars).toHaveLength(5)
  })

  it('calls onQuickView when quick view button is clicked', async () => {
    const user = userEvent.setup()
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    // Hover to show action buttons
    const card = screen.getByRole('link')
    await user.hover(card)
    
    await waitFor(() => {
      const quickViewButton = screen.getByText('Quick View')
      expect(quickViewButton).toBeInTheDocument()
    })
    
    const quickViewButton = screen.getByText('Quick View')
    await user.click(quickViewButton)
    
    expect(mockOnQuickView).toHaveBeenCalledWith(mockProduct)
  })

  it('toggles favorite status when heart button is clicked', async () => {
    const user = userEvent.setup()
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    const favoriteButton = screen.getByRole('button', { name: /heart/i })
    await user.click(favoriteButton)
    
    // Should toggle the favorite state (implementation would need to be updated to test this properly)
    expect(favoriteButton).toBeInTheDocument()
  })

  it('shows different button text based on product options', () => {
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    // Product has sizes and colors, so should show "Select Options"
    // This would be visible on hover
    const card = screen.getByRole('link')
    fireEvent.mouseEnter(card)
    
    // Note: The actual button visibility depends on hover state and animation timing
    // In a real test, you might need to wait for animations or use different testing strategies
  })

  it('handles image cycling on hover', async () => {
    const user = userEvent.setup()
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    const card = screen.getByRole('link')
    await user.hover(card)
    
    // Should show image indicators for multiple images
    await waitFor(() => {
      const indicators = screen.getAllByRole('button')
      // Should have indicators for image cycling plus other buttons
      expect(indicators.length).toBeGreaterThan(0)
    })
  })

  it('displays no image placeholder when no images available', () => {
    const noImageProduct = { ...mockProduct, images: [] }
    render(<ProductCard product={noImageProduct} onQuickView={mockOnQuickView} />)
    
    expect(screen.getByText('No image')).toBeInTheDocument()
  })

  it('applies correct styling for hover effects', async () => {
    const user = userEvent.setup()
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    const card = screen.getByRole('link')
    
    // Test hover state
    await user.hover(card)
    
    // The card should have hover classes applied
    expect(card.closest('div')).toBeInTheDocument()
  })

  it('handles add to cart functionality', async () => {
    const user = userEvent.setup()
    render(<ProductCard product={mockProduct} onQuickView={mockOnQuickView} />)
    
    const card = screen.getByRole('link')
    await user.hover(card)
    
    await waitFor(() => {
      const addToCartButton = screen.getByText(/Select/)
      expect(addToCartButton).toBeInTheDocument()
    })
  })
})
