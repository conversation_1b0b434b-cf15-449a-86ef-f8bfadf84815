"use client";

import { ProtectedRoute } from '@/components/auth/protected-route';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';

interface Partner {
  id: string;
  name: string;
  surname: string;
  email: string;
  cellNumber: string;
  otherCellNumber?: string;
  referralCode: string;
  discountAmount: number;
  discountCode: string;
  isActive: boolean;
  commissionEarned: number;
  bonusPaid: number;
  createdAt: string;
}

export default function EditPartnerPage() {
  const [form, setForm] = useState({
    name: '',
    surname: '',
    email: '',
    cellNumber: '',
    otherCellNumber: '',
    referralCode: '',
    discountAmount: '',
    discountCode: '',
    isActive: true,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();
  const params = useParams();
  const partnerId = params.id as string;

  useEffect(() => {
    if (partnerId) {
      fetchPartner();
    }
  }, [partnerId]);

  const fetchPartner = async () => {
    try {
      const response = await fetch(`/api/admin/partners/${partnerId}`);
      const data = await response.json();

      if (response.ok) {
        const partner = data.partner;
        setForm({
          name: partner.name,
          surname: partner.surname,
          email: partner.email,
          cellNumber: partner.cellNumber,
          otherCellNumber: partner.otherCellNumber || '',
          referralCode: partner.referralCode,
          discountAmount: partner.discountAmount?.toString() || '',
          discountCode: partner.discountCode || '',
          isActive: partner.isActive,
        });
      } else {
        setError(data.error || 'Failed to fetch partner');
      }
    } catch (error) {
      setError('Failed to fetch partner');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string | boolean) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError('');

    try {
      const response = await fetch(`/api/admin/partners/${partnerId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update partner');
      }

      // Success - redirect to partners list
      router.push('/admin/partners?message=partner_updated');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update partner');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ProtectedRoute requiredRole="ADMIN">
        <div className="max-w-xl mx-auto py-8 px-2 md:px-0">
          <div className="text-center">Loading partner...</div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRole="ADMIN">
      <div className="max-w-xl mx-auto py-8 px-2 md:px-0">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Edit Sales Partner</h1>
          <Button variant="outline" onClick={() => router.back()}>
            Back
          </Button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6 bg-white rounded shadow p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium">Name *</label>
              <Input
                required
                value={form.name}
                onChange={e => handleChange('name', e.target.value)}
                placeholder="e.g. Lebohang"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">Surname *</label>
              <Input
                required
                value={form.surname}
                onChange={e => handleChange('surname', e.target.value)}
                placeholder="e.g. Mokoena"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium">Email *</label>
              <Input
                required
                type="email"
                value={form.email}
                onChange={e => handleChange('email', e.target.value)}
                placeholder="e.g. <EMAIL>"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">Cell Number *</label>
              <Input
                required
                value={form.cellNumber}
                onChange={e => handleChange('cellNumber', e.target.value)}
                placeholder="e.g. +266 6000 0000"
              />
            </div>
          </div>
          
          <div>
            <label className="block mb-1 font-medium">Other Cell Number</label>
            <Input
              value={form.otherCellNumber}
              onChange={e => handleChange('otherCellNumber', e.target.value)}
              placeholder="Optional"
            />
          </div>
          
          <div>
            <label className="block mb-1 font-medium">Referral Code *</label>
            <Input
              required
              value={form.referralCode}
              onChange={e => handleChange('referralCode', e.target.value)}
              placeholder="e.g. LEBO01"
              className="uppercase"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium">Discount Amount *</label>
              <Input
                required
                type="number"
                value={form.discountAmount}
                onChange={e => handleChange('discountAmount', e.target.value)}
                placeholder="e.g. 50"
                min="0"
                step="0.01"
              />
              <p className="text-sm text-gray-500 mt-1">Amount in Maloti (M)</p>
            </div>
            <div>
              <label className="block mb-1 font-medium">Discount Code *</label>
              <Input
                required
                value={form.discountCode}
                onChange={e => handleChange('discountCode', e.target.value)}
                placeholder="e.g. PARTNER50"
                className="uppercase"
              />
              <p className="text-sm text-gray-500 mt-1">Will be auto-applied when QR is scanned</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isActive"
              checked={form.isActive}
              onChange={e => handleChange('isActive', e.target.checked)}
              className="rounded"
            />
            <label htmlFor="isActive" className="font-medium">
              Partner is active
            </label>
          </div>
          
          <div className="pt-4">
            <Button type="submit" className="w-full" disabled={saving}>
              {saving ? 'Updating Partner...' : 'Update Partner'}
            </Button>
          </div>
        </form>
      </div>
    </ProtectedRoute>
  );
} 