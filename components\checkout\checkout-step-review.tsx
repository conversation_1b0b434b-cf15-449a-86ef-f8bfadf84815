"use client";

import { CheckoutData } from "./checkout-content";
import { useCart } from "@/contexts/cart-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, ArrowRight, Edit } from "lucide-react";
import { formatPrice, getEffectivePrice, calculateDeliveryFee, getFinalOrderTotal } from "@/lib/product-utils";

interface CheckoutStepReviewProps {
  data: CheckoutData;
  onUpdate: (data: Partial<CheckoutData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export default function CheckoutStepReview({
  data,
  onUpdate,
  onNext,
  onPrev,
}: CheckoutStepReviewProps) {
  const { state: cartState } = useCart();

  const subtotal = cartState.totalPrice;
  const discountAmount = data.discountAmount || 0;
  const deliveryInfo = calculateDeliveryFee(data.shipping.district, subtotal - discountAmount);
  const deliveryCost = deliveryInfo.fee;
  const total = getFinalOrderTotal(subtotal, discountAmount, deliveryCost);

  return (
    <div className="space-y-6">
      {/* Shipping Information Review */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-lg">Shipping Information</CardTitle>
          <Button variant="outline" size="sm" onClick={onPrev}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                Contact Information
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>{data.shipping.fullName}</p>
                <p>{data.shipping.email}</p>
                <p>{data.shipping.phone}</p>
                {data.shipping.notes && (
                  <p className="italic text-blue-700">Lay Buy Details: {data.shipping.notes}</p>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                Delivery Address
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>{data.shipping.address}</p>
                <p>
                  {data.shipping.city}, {data.shipping.postalCode}
                </p>
                <p>{data.shipping.country}</p>
                {data.shipping.notes && (
                  <p className="italic">Note: {data.shipping.notes}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Items Review */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Order Items ({cartState.totalItems})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {cartState.items.map((item) => {
              const effectivePrice = getEffectivePrice(item.product);
              
              return (
                <div
                  key={item.id}
                  className="flex gap-4 py-4 border-b last:border-b-0"
                >
                  <div className="flex-shrink-0">
                    <img
                      src={item.product.images[0] || "/placeholder.png"}
                      alt={item.product.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">
                      {item.product.name}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {item.product.brand}
                    </p>

                    <div className="flex items-center gap-2 mt-1">
                      {item.size && (
                        <Badge variant="outline" className="text-xs">
                          Size: {item.size}
                        </Badge>
                      )}
                      {item.color && (
                        <Badge variant="outline" className="text-xs">
                          Color: {item.color}
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center justify-between mt-2">
                      <span className="text-sm text-gray-600">
                        Quantity: {item.quantity}
                      </span>
                      <div className="text-right">
                        <div className="font-medium">
                          {formatPrice(effectivePrice * item.quantity)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatPrice(effectivePrice)} each
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Order Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>Subtotal ({cartState.totalItems} items)</span>
              <span>{formatPrice(subtotal)}</span>
            </div>

            {discountAmount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>
                  Discount {data.discountCode && `(${data.discountCode})`}
                </span>
                <span>-{formatPrice(discountAmount)}</span>
              </div>
            )}

            <div className="flex justify-between">
              <span>Delivery</span>
              <span className={deliveryInfo.isFree ? "text-green-600" : ""}>
                {deliveryInfo.isFree ? "Free" : formatPrice(deliveryCost)}
              </span>
            </div>

            <Separator />

            <div className="flex justify-between font-semibold text-lg">
              <span>Total</span>
              <span>{formatPrice(total)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delivery Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Delivery Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Standard delivery: 3-5 business days</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              {deliveryInfo.reason}
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span>You will receive order confirmation via email</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex flex-col md:flex-row md:justify-between pt-4 gap-2">
        <Button variant="outline" onClick={onPrev}>
          <ArrowLeft className="mr-2 h-5 w-5" />
          Back to Delivery
        </Button>

        <Button onClick={onNext} size="lg">
          Proceed to Payment
          <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </div>
  );
}
