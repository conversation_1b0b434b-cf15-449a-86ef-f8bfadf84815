"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  CreditCard,
  Upload,
  CheckCircle,
  AlertTriangle,
  AlertCircle,
  Smartphone,
  Building,
  X,
  Info,
} from "lucide-react";
import { formatLayBuyPrice } from "@/lib/lay-buy-utils";
import { formatPrice } from "@/lib/product-utils";
import { UploadButton } from "@/lib/uploadthing";

interface LayBuyPaymentFormProps {
  orderId: string;
  orderNumber: string;
  remainingBalance: number;
  onPaymentAdded: (amount: number) => void;
}

export default function LayBuyPaymentForm({
  orderId,
  orderNumber,
  remainingBalance,
  onPaymentAdded,
}: LayBuyPaymentFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    amount: "",
    paymentMethod: "",
    paymentProof: "",
    notes: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [paymentProofUrl, setPaymentProofUrl] = useState<string>("");
  const [uploadError, setUploadError] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [amountError, setAmountError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!paymentProofUrl) {
      setUploadError("Please upload payment proof before submitting the payment");
      return;
    }

    setIsSubmitting(true);
    setErrors({});
    setUploadError("");
    setErrorMessage(null);
    setSuccessMessage(null);

    // Validate form
    const newErrors: Record<string, string> = {};
    const paymentAmount = parseFloat(formData.amount);

    if (!formData.amount || paymentAmount <= 0) {
      newErrors.amount = "Please enter a valid payment amount";
    } else if (paymentAmount > remainingBalance) {
      newErrors.amount = `You shouldn't pay more than the balance of ${formatLayBuyPrice(remainingBalance)}`;
      setErrorMessage(`You shouldn't pay more than the balance of ${formatLayBuyPrice(remainingBalance)}`);
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    if (!formData.paymentMethod) {
      newErrors.paymentMethod = "Please select a payment method";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Submit payment
      const response = await fetch(`/api/lay-buy-orders/${orderId}/payments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: paymentAmount,
          paymentMethod: formData.paymentMethod,
          paymentProof: paymentProofUrl,
          notes: formData.notes,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setFormData({
          amount: "",
          paymentMethod: "",
          paymentProof: "",
          notes: "",
        });
        setPaymentProofUrl("");
        setShowForm(false);
        onPaymentAdded(paymentAmount);

        if (paymentAmount === remainingBalance) {
          setSuccessMessage('Congrats, you will receive your new pair soon!');
          setTimeout(() => {
            router.push('/orders?message=laybuy_complete');
          }, 2000);
        } else {
          setSuccessMessage('Payment submitted! Your payment will be reviewed by the admin.');
          setTimeout(() => {
            setSuccessMessage(null);
          }, 4000);
        }
      } else {
        setErrors({ submit: result.error || "Failed to submit payment" });
        setErrorMessage(result.error || "Failed to submit payment");
      }
    } catch (error) {
      setErrors({ submit: "Failed to submit payment. Please try again." });
      setErrorMessage("Failed to submit payment. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (field === "amount") {
      const paymentAmount = parseFloat(value);
      if (paymentAmount > remainingBalance) {
        setAmountError(`Amount cannot exceed the maximum of ${formatLayBuyPrice(remainingBalance)}`);
      } else {
        setAmountError(null);
      }
    }
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  if (!showForm) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Make a Payment
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center p-6 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {formatLayBuyPrice(remainingBalance)}
            </div>
            <div className="text-blue-800">Remaining Balance</div>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              You can make partial payments at any time. All payments require verification before being applied to your balance.
            </AlertDescription>
          </Alert>

          <Button 
            onClick={() => setShowForm(true)} 
            className="w-full"
            size="lg"
          >
            <CreditCard className="mr-2 h-5 w-5" />
            Make Payment
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {successMessage && (
        <Alert className="mb-4 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className='text-green-800'>{successMessage}</AlertDescription>
        </Alert>
      )}
      {errorMessage && (
        <Alert className="mb-4 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className='text-red-800'>{errorMessage}</AlertDescription>
        </Alert>
      )}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Make a Payment - Order {orderNumber}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
          {/* Payment Amount */}
          <div>
            <Label htmlFor="amount">Payment Amount (M)</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0.01"
              max={remainingBalance}
              placeholder="0.00"
              value={formData.amount}
              onChange={(e) => handleInputChange("amount", e.target.value)}
              className={errors.amount || amountError ? "border-red-500" : ""}
            />
            {(errors.amount || amountError) && (
              <p className="text-sm text-red-600 mt-1">{errors.amount || amountError}</p>
            )}
            <p className="text-sm text-gray-600 mt-1">
              Maximum: {formatLayBuyPrice(remainingBalance)}
            </p>
          </div>

          {/* Payment Method Selection */}
          <div>
            <Label className="text-base font-medium">Select Payment Method</Label>
            <RadioGroup
              value={formData.paymentMethod}
              onValueChange={(value) => handleInputChange("paymentMethod", value)}
              className="mt-3"
            >
              {/* M-Pesa */}
              <div className="flex items-center space-x-2 p-4 border rounded-lg">
                <RadioGroupItem value="mpesa" id="mpesa" />
                <Label htmlFor="mpesa" className="flex items-center gap-3 cursor-pointer flex-1">
                  <Smartphone className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium">M-Pesa</div>
                    <div className="text-sm text-gray-500">
                      Send money via M-Pesa and upload payment proof
                    </div>
                  </div>
                </Label>
              </div>

              {/* EcoCash */}
              <div className="flex items-center space-x-2 p-4 border rounded-lg">
                <RadioGroupItem value="ecocash" id="ecocash" />
                <Label htmlFor="ecocash" className="flex items-center gap-3 cursor-pointer flex-1">
                  <Smartphone className="h-5 w-5 text-orange-600" />
                  <div>
                    <div className="font-medium">EcoCash</div>
                    <div className="text-sm text-gray-500">
                      Send money via EcoCash and upload payment proof
                    </div>
                  </div>
                </Label>
              </div>

              {/* Bank Transfer */}
              <div className="flex items-center space-x-2 p-4 border rounded-lg">
                <RadioGroupItem value="bank_transfer" id="bank_transfer" />
                <Label htmlFor="bank_transfer" className="flex items-center gap-3 cursor-pointer flex-1">
                  <Building className="h-5 w-5 text-blue-600" />
                  <div>
                    <div className="font-medium">Bank Transfer</div>
                    <div className="text-sm text-gray-500">
                      Transfer to our bank account and upload payment proof
                    </div>
                  </div>
                </Label>
              </div>
            </RadioGroup>
            {errors.paymentMethod && (
              <p className="text-sm text-red-600 mt-1">{errors.paymentMethod}</p>
            )}
          </div>

        </form>
      </CardContent>
    </Card>

    {/* M-Pesa Payment Details */}
    {showForm && formData.paymentMethod === "mpesa" && (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">M-Pesa Payment Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <h4 className="font-medium text-green-900 mb-3">Send money to:</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-green-700">Phone Number:</span>
                <span className="font-medium">+266 5316 3354</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Account Holder:</span>
                <span className="font-medium">Katleho Namane</span>
              </div>
              <Separator className="my-2" />
              <div className="flex justify-between font-semibold">
                <span className="text-green-700">Amount to Send:</span>
                <span className="text-lg">{formData.amount ? formatPrice(parseFloat(formData.amount)) : formatLayBuyPrice(remainingBalance)}</span>
              </div>
            </div>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Send the exact amount via M-Pesa to +266 5316 3354 (Katleho Namane).
              After sending, upload a screenshot of the payment confirmation below.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )}

    {/* EcoCash Payment Details */}
    {showForm && formData.paymentMethod === "ecocash" && (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">EcoCash Payment Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
            <h4 className="font-medium text-orange-900 mb-3">Send money to:</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-orange-700">Phone Number:</span>
                <span className="font-medium">+266 6284 4473</span>
              </div>
              <div className="flex justify-between">
                <span className="text-orange-700">Account Holder:</span>
                <span className="font-medium">Katleho Namane</span>
              </div>
              <Separator className="my-2" />
              <div className="flex justify-between font-semibold">
                <span className="text-orange-700">Amount to Send:</span>
                <span className="text-lg">{formData.amount ? formatPrice(parseFloat(formData.amount)) : formatLayBuyPrice(remainingBalance)}</span>
              </div>
            </div>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Send the exact amount via EcoCash to +266 6284 4473 (Katleho Namane).
              After sending, upload a screenshot of the payment confirmation below.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )}

    {/* Bank Transfer Details */}
    {showForm && formData.paymentMethod === "bank_transfer" && (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Bank Transfer Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Please contact us at +266 6284 4473 or <EMAIL> for bank transfer details.
              Include your order number {orderNumber} in your message.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )}

    {/* Payment Proof Upload */}
    {showForm && formData.paymentMethod && (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Upload Payment Proof</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!paymentProofUrl ? (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-sm text-gray-600 mb-4">
                Upload a screenshot of your payment confirmation
              </p>
              <UploadButton
                endpoint="paymentProofUploader"
                onClientUploadComplete={(res) => {
                  if (res?.[0]?.url) {
                    setPaymentProofUrl(res[0].url);
                    setUploadError("");
                  }
                }}
                onUploadError={(error: Error) => {
                  setUploadError(`Upload failed: ${error.message}`);
                }}
                appearance={{
                  button: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",
                  allowedContent: "text-xs text-gray-600 mt-2",
                }}
              />
            </div>
          ) : (
            <div className="border border-green-200 bg-green-50 rounded-lg p-4 flex flex-col items-center">
              <div className="flex items-center justify-between w-full mb-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-green-800">Payment proof uploaded successfully</span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setPaymentProofUrl("")}
                  className="text-green-600 hover:text-green-800"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              {/* Image preview */}
              <img
                src={paymentProofUrl}
                alt="Payment proof preview"
                className="rounded-lg max-h-48 object-contain border border-gray-200 bg-white"
                style={{ maxWidth: '100%', marginTop: 8 }}
              />
            </div>
          )}

          {uploadError && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-red-600">
                {uploadError}
              </AlertDescription>
            </Alert>
          )}

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Any additional information about your payment"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              rows={3}
            />
          </div>

          {/* Submit Button */}
          <div className="flex gap-3">
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !paymentProofUrl || !!amountError}
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Submit Payment
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowForm(false);
                setPaymentProofUrl("");
                setFormData({
                  amount: "",
                  paymentMethod: "",
                  paymentProof: "",
                  notes: "",
                });
              }}
            >
              Cancel
            </Button>
          </div>

          {/* Error Display */}
          {errors.submit && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-red-600">
                {errors.submit}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    )}
    </div>
  );
}
