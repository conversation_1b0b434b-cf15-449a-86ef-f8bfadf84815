#!/usr/bin/env node

/**
 * Quick API Test - Just test the core APIs needed for enhancement
 */

const { GoogleGenerativeAI } = require("@google/generative-ai");
const axios = require('axios');

async function testAPIs() {
  console.log('🧪 Quick API Test');
  console.log('=================\n');

  // Test Gemini AI
  console.log('🧠 Testing Gemini AI...');
  try {
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const result = await model.generateContent('Identify this product: Nike Air Jordan 1 Red and Black sneakers');
    const response = await result.response;
    
    console.log('✅ Gemini AI: Working!');
    console.log(`   Response: ${response.text().substring(0, 100)}...`);
  } catch (error) {
    console.log(`❌ Gemini AI: ${error.message}`);
    return;
  }

  // Test Google Custom Search
  console.log('\n🔍 Testing Google Custom Search...');
  try {
    const apiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
    const searchEngineId = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID;
    
    console.log(`   API Key: ${apiKey ? apiKey.substring(0, 10) + '...' : 'MISSING'}`);
    console.log(`   Engine ID: ${searchEngineId || 'MISSING'}`);
    
    const testUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=nike+air+jordan+white+background&searchType=image&num=3`;
    
    console.log('   Making test search...');
    const response = await axios.get(testUrl);
    
    if (response.data && response.data.items) {
      console.log('✅ Google Custom Search: Working!');
      console.log(`   Found ${response.data.items.length} results`);
      console.log(`   First result: ${response.data.items[0].link}`);
    } else {
      console.log('⚠️  Google Custom Search: No results returned');
    }
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('❌ Google Custom Search: Invalid API key or Search Engine ID');
      console.log('   Check your .env file for complete API key');
    } else if (error.response?.status === 403) {
      console.log('❌ Google Custom Search: API quota exceeded or billing issue');
    } else {
      console.log(`❌ Google Custom Search: ${error.message}`);
    }
    return;
  }

  console.log('\n🎉 All APIs are working! You can proceed with enhancement.');
}

testAPIs().catch(console.error);
