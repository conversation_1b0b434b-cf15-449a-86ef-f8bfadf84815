import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import QuickViewModal from '@/components/products/quick-view-modal'
import { Product } from '@/utils/types'

// Mock product data
const mockProduct: Product = {
  id: 'test-product-1',
  name: 'Test Sneaker',
  description: 'A premium test sneaker with excellent quality and comfort.',
  price: 999.99,
  discountedPrice: 799.99,
  brand: 'TestBrand',
  categoryId: 'test-category',
  images: [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg'
  ],
  sizes: ['8', '9', '10'],
  colors: ['Black', 'White'],
  stock: 10,
  isActive: true,
  rating: 4.5,
  reviewCount: 25,
  costPrice: null,
  shippingFee: null,
  lateCollectionFee: null,
  totalCost: null,
  costPriceUpdatedAt: null,
  costPriceUpdatedBy: null,
  feesUpdatedAt: null,
  feesUpdatedBy: null,

  createdAt: new Date(),
  updatedAt: new Date(),
  category: {
    id: 'test-category',
    name: 'Test Category',
    description: 'Test category description',
    image: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    products: []
  },
  reviews: [],
  _count: { reviews: 25, orderItems: 0 },
  isFeatured: false
}

describe('QuickViewModal', () => {
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders modal when open with product data', () => {
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    expect(screen.getByText('Test Sneaker')).toBeInTheDocument()
    expect(screen.getByText('TestBrand')).toBeInTheDocument()
    expect(screen.getByText('M799.99')).toBeInTheDocument()
    expect(screen.getByText('M999.99')).toBeInTheDocument()
    expect(screen.getByText('A premium test sneaker with excellent quality and comfort.')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={false} 
        onClose={mockOnClose} 
      />
    )
    
    expect(screen.queryByText('Test Sneaker')).not.toBeInTheDocument()
  })

  it('does not render when product is null', () => {
    render(
      <QuickViewModal 
        product={null} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    expect(screen.queryByText('Test Sneaker')).not.toBeInTheDocument()
  })

  it('displays discount badge for sale products', () => {
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    expect(screen.getByText('-20% OFF')).toBeInTheDocument()
  })

  it('shows stock status correctly', () => {
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    expect(screen.getByText('✓ In Stock (10 available)')).toBeInTheDocument()
  })

  it('displays out of stock status', () => {
    const outOfStockProduct = { ...mockProduct, stock: 0 }
    render(
      <QuickViewModal 
        product={outOfStockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    expect(screen.getByText('✗ Out of Stock')).toBeInTheDocument()
  })

  it('allows size selection', async () => {
    const user = userEvent.setup()
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const sizeSelect = screen.getByText('Select a size')
    await user.click(sizeSelect)
    
    await waitFor(() => {
      expect(screen.getByText('8')).toBeInTheDocument()
      expect(screen.getByText('9')).toBeInTheDocument()
      expect(screen.getByText('10')).toBeInTheDocument()
    })
  })

  it('allows color selection', async () => {
    const user = userEvent.setup()
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const colorSelect = screen.getByText('Select a color')
    await user.click(colorSelect)
    
    await waitFor(() => {
      expect(screen.getByText('Black')).toBeInTheDocument()
      expect(screen.getByText('White')).toBeInTheDocument()
    })
  })

  it('allows quantity adjustment', async () => {
    const user = userEvent.setup()
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const quantityDisplay = screen.getByText('1')
    expect(quantityDisplay).toBeInTheDocument()
    
    const increaseButton = screen.getByRole('button', { name: /plus/i })
    await user.click(increaseButton)
    
    expect(screen.getByText('2')).toBeInTheDocument()
    
    const decreaseButton = screen.getByRole('button', { name: /minus/i })
    await user.click(decreaseButton)
    
    expect(screen.getByText('1')).toBeInTheDocument()
  })

  it('prevents quantity below 1', async () => {
    const user = userEvent.setup()
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const decreaseButton = screen.getByRole('button', { name: /minus/i })
    expect(decreaseButton).toBeDisabled()
  })

  it('prevents quantity above stock', async () => {
    const lowStockProduct = { ...mockProduct, stock: 2 }
    const user = userEvent.setup()
    render(
      <QuickViewModal 
        product={lowStockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const increaseButton = screen.getByRole('button', { name: /plus/i })
    await user.click(increaseButton) // quantity = 2
    
    expect(increaseButton).toBeDisabled()
  })

  it('shows image thumbnails when multiple images exist', () => {
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const thumbnails = screen.getAllByAltText(/Test Sneaker \d+/)
    expect(thumbnails).toHaveLength(2)
  })

  it('allows image switching via thumbnails', async () => {
    const user = userEvent.setup()
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const secondThumbnail = screen.getByAltText('Test Sneaker 2')
    await user.click(secondThumbnail)
    
    // The main image should switch (this would need more specific testing in a real scenario)
    expect(secondThumbnail.closest('button')).toHaveClass('border-blue-500')
  })

  it('toggles favorite status', async () => {
    const user = userEvent.setup()
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const favoriteButton = screen.getByText('Add to Favorites')
    await user.click(favoriteButton)
    
    expect(screen.getByText('Favorited')).toBeInTheDocument()
  })

  it('validates size selection before adding to cart', async () => {
    const user = userEvent.setup()
    
    // Mock window.alert
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {})
    
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const addToCartButton = screen.getByText('Add to Cart')
    await user.click(addToCartButton)
    
    expect(alertSpy).toHaveBeenCalledWith('Please select a size')
    
    alertSpy.mockRestore()
  })

  it('validates color selection before adding to cart', async () => {
    const user = userEvent.setup()
    
    // Mock window.alert
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {})
    
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    // Select size first
    const sizeSelect = screen.getByText('Select a size')
    await user.click(sizeSelect)
    await user.click(screen.getByText('9'))
    
    const addToCartButton = screen.getByText('Add to Cart')
    await user.click(addToCartButton)
    
    expect(alertSpy).toHaveBeenCalledWith('Please select a color')
    
    alertSpy.mockRestore()
  })

  it('renders rating stars correctly', () => {
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    expect(screen.getByText('(25 reviews)')).toBeInTheDocument()
  })

  it('has link to full product details', () => {
    render(
      <QuickViewModal 
        product={mockProduct} 
        isOpen={true} 
        onClose={mockOnClose} 
      />
    )
    
    const viewDetailsLink = screen.getByText('View Full Details')
    expect(viewDetailsLink.closest('a')).toHaveAttribute('href', '/products/test-product-1')
  })
})
