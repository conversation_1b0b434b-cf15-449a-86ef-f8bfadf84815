// scripts/update-duplicate-product-names-with-gemini.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function main() {
  console.log('🔍 Finding duplicate product names...');

  // 1. Find duplicate product names
  const duplicates: { name: string }[] = await prisma.$queryRaw`
    SELECT name
    FROM "product"
    GROUP BY name
    HAVING COUNT(*) > 1
  `;

  if (duplicates.length === 0) {
    console.log('✅ No duplicate product names found. Exiting.');
    return;
  }

  const duplicateNames = duplicates.map(d => d.name);
  console.log(`🔥 Found ${duplicateNames.length} duplicate product name(s):`, duplicateNames);

  // 2. For each duplicate name, process the corresponding products
  for (const name of duplicateNames) {
    const productsToUpdate = await prisma.product.findMany({
      where: { name: name },
    });

    console.log(`\n--- Processing ${productsToUpdate.length} products named "${name}" ---`);

    for (const product of productsToUpdate) {
      const imageUrl = product.images[0];

      if (!imageUrl) {
        console.log(`⏭️  Skipping product ${product.id} ("${product.name}") - no image found.`);
        continue;
      }

      console.log(`🤖 Analyzing image for product ${product.id} (${product.name}) with URL: ${imageUrl}`);

      try {
        const res = await fetch('http://localhost:3000/api/analyze-sneaker', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ imageUrl }),
        });

        if (!res.ok) {
          const errorBody = await res.text();
          console.error(`❌ API Error for product ${product.id}: ${res.status} ${res.statusText}`, errorBody);
          continue;
        }

        const data = await res.json();

        if (data && data.description) {
          let geminiResult;
          try {
            geminiResult = JSON.parse(data.description);
          } catch {
            console.log(`⚠️  Could not parse Gemini response for product ${product.id}, skipping.`);
            continue;
          }

          const newName = geminiResult.name;

          if (newName && newName !== product.name) {
            await prisma.product.update({
              where: { id: product.id },
              data: { name: newName },
            });
            console.log(`✅ Updated product ${product.id}: "${product.name}" → "${newName}"`);
          } else {
            console.log(`🤔 Gemini did not suggest a new name for product ${product.id}, skipping.`);
          }
        } else {
          console.log(`🤷 No description from Gemini for product ${product.id}, skipping.`);
        }
      } catch (err) {
        console.error(`💥 Critical error analyzing product ${product.id}:`, err);
      }
      // Add a 5-second delay to stay within free tier limits
      await sleep(5000);
    }
  }
}

main()
  .catch((e) => {
    console.error('❌ Script failed with an error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
    console.log('\n🏁 Script finished.');
  }); 