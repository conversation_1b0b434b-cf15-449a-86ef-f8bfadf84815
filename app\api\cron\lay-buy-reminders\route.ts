import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { sendLayBuyReminder } from "@/lib/email-service";
import { calculateReminderWeek, getReminderUrgency, calculateDaysRemaining } from "@/lib/lay-buy-utils";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// POST /api/cron/lay-buy-reminders - Send automated Lay-Buy reminders
export async function POST(request: NextRequest) {
  try {
    // Verify this is a legitimate cron request (you might want to add authentication)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    console.log('Starting Lay-Buy reminder cron job...');

    // Get all active Lay-Buy orders
    const activeOrders = await prisma.layBuyOrder.findMany({
      where: {
        status: 'ACTIVE',
      },
      include: {
        user: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        reminders: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    console.log(`Found ${activeOrders.length} active Lay-Buy orders`);

    const results = {
      processed: 0,
      reminders_sent: 0,
      errors: 0,
      skipped: 0,
    };

    for (const order of activeOrders) {
      try {
        results.processed++;

        const now = new Date();
        const createdAt = new Date(order.createdAt);
        const dueDate = order.dueDate ? new Date(order.dueDate) : null;
        const gracePeriodEnd = order.gracePeriodEnd ? new Date(order.gracePeriodEnd) : null;

        // Calculate which week we're in
        const weekNumber = calculateReminderWeek(createdAt, now);
        const timeRemaining = calculateDaysRemaining(
          dueDate ?? new Date(0),
          gracePeriodEnd ?? new Date(0),
          now
        );

        // Determine reminder type and urgency
        const { urgency, type } = getReminderUrgency(weekNumber);

        // Check if we should send a reminder
        let shouldSendReminder = false;
        let reminderType = type;

        // Check if order is forfeited (past grace period)
        if (timeRemaining.status === 'forfeited') {
          // Mark order as forfeited if not already
          if (order.status === 'ACTIVE') {
            await prisma.layBuyOrder.update({
              where: { id: order.id },
              data: {
                status: 'FORFEITED',
                forfeitedAt: now,
              },
            });
            console.log(`Order ${order.orderNumber} marked as forfeited`);
          }
          results.skipped++;
          continue;
        }

        // Check if we've already sent a reminder this week
        const lastReminder = order.reminders[0];
        const daysSinceLastReminder = lastReminder && lastReminder.sentAt
          ? Math.floor(
              (now.getTime() - new Date(lastReminder.sentAt ?? 0).getTime()) / (1000 * 60 * 60 * 24)
            )
          : 999;

        // Send weekly reminders (every 7 days)
        if (weekNumber <= 5 && daysSinceLastReminder >= 7) {
          shouldSendReminder = true;
          reminderType = 'WEEKLY';
        }
        // Send urgent reminder in week 6
        else if (weekNumber === 6 && daysSinceLastReminder >= 3) {
          shouldSendReminder = true;
          reminderType = 'URGENT';
        }
        // Send grace period reminders (every 2 days)
        else if (weekNumber >= 7 && timeRemaining.isInGracePeriod && daysSinceLastReminder >= 2) {
          shouldSendReminder = true;
          reminderType = timeRemaining.daysRemaining <= 2 ? 'FINAL_NOTICE' : 'GRACE_PERIOD';
        }

        if (shouldSendReminder) {
          console.log(`Sending ${reminderType} reminder for order ${order.orderNumber} (Week ${weekNumber})`);

          // Send the reminder email
          const emailResult = await sendLayBuyReminder(order, reminderType, weekNumber);

          if (emailResult.success) {
            // Record the reminder in the database
            await prisma.layBuyReminder.create({
              data: {
                layBuyOrderId: order.id,
                weekNumber,
                reminderType,
                emailSent: true,
                smsSent: false, // SMS not implemented yet
              },
            });

            results.reminders_sent++;
            console.log(`Reminder sent successfully for order ${order.orderNumber}`);
          } else {
            console.error(`Failed to send reminder for order ${order.orderNumber}:`, emailResult.error);
            results.errors++;
          }
        } else {
          results.skipped++;
        }

      } catch (error) {
        console.error(`Error processing order ${order.orderNumber}:`, error);
        results.errors++;
      }
    }

    console.log('Lay-Buy reminder cron job completed:', results);

    const response: ApiResponse<typeof results> = {
      success: true,
      data: results,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in Lay-Buy reminder cron job:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process Lay-Buy reminders" },
      { status: 500 }
    );
  }
}

// GET /api/cron/lay-buy-reminders - Get reminder status (for monitoring)
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get statistics about active orders and recent reminders
    const [activeOrdersCount, recentReminders, overdueOrders] = await Promise.all([
      prisma.layBuyOrder.count({
        where: { status: 'ACTIVE' },
      }),
      prisma.layBuyReminder.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      }),
      prisma.layBuyOrder.count({
        where: {
          status: 'ACTIVE',
          dueDate: {
            lt: new Date(),
          },
        },
      }),
    ]);

    const stats = {
      active_orders: activeOrdersCount,
      reminders_sent_24h: recentReminders,
      overdue_orders: overdueOrders,
      last_check: new Date().toISOString(),
    };

    const response: ApiResponse<typeof stats> = {
      success: true,
      data: stats,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error getting reminder status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to get reminder status" },
      { status: 500 }
    );
  }
}
