"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Star,
  ShoppingBag,
  Heart,
  CheckCircle,
  Box,
  Sparkles,
  TrendingUp,
  Award,
  Zap,
  Shield,
  Truck,
  Package
} from "lucide-react";
import NavBar from "@/components/navbar";
import Footer from "@/components/footer";
import { useSession } from "@/lib/auth-client";
import { User } from "@/utils/types";
import { getUserById } from "@/actions/userActions";

import NavBarSkeleton from "@/components/navBarSkeleton";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

// Featured products with actual images and discounted prices from the database
const featuredProducts = [
  // Air Jordan 7 Retros - Premium flagship models (using actual Jordan 7 images)
  {
    id: "cmd7uzms2000ljs04niwb2kbe",
    name: "Air Jordan 7 Retro 'Black/Metallic Gold'",
    price: "M 1,540.00",
    image: "https://utfs.io/f/9WixNlVtj4Jy3Bd5vpm9DB1GCkftWY9AMsrE0UQ5vqdj64ul",
    badge: "Premium",
  },
  {
    id: "cmd7uzmsv000tjs04wlcxv4q0",
    name: "Air Jordan 7 Retro 'White/Black/Red'",
    price: "M 1,540.00",
    image: "https://utfs.io/f/9WixNlVtj4JydvxssAiIUKhbov3g216wAnH4mO9GLyNjPeux",
    badge: "Premium",
  },
  // Adidas ZX 2K Boost - DISCOUNTED Adidas model
  {
    id: "cmcza1yyz0003jy04g7e1d5te",
    name: "Adidas ZX 2K Boost",
    price: "M 1,390.00",
    originalPrice: "M 1,610.00",
    image: "https://utfs.io/f/9WixNlVtj4Jyc2jL6VDsHhTOMG15xLAeufpF3IZC6g9vWyza",
    badge: "Sale",
  },
  // Air Jordan 4 Retro Black/Metallic Gold
  {
    id: "cmcs0ldxe0009jx046cbucr1r",
    name: "Air Jordan 4 Retro 'Black/Metallic Gold'",
    price: "M 1,350.00",
    image: "https://utfs.io/f/9WixNlVtj4Jy7xKiXc3I6saGExtbDoPXQmKrqwcOZTnd0v49",
    badge: "Iconic",
  },
  // Puma Suede
  {
    id: "cmd7uzmo90001js04tve60fzv",
    name: "Puma Suede",
    price: "M 1,050.00",
    image: "https://utfs.io/f/9WixNlVtj4Jyct84PAADsHhTOMG15xLAeufpF3IZC6g9vWyz",
    badge: "Classic",
  },
  // New Balance 327 Sea Salt/Navy
  {
    id: "cmcnzqrtb000hlf04ypfvi90y",
    name: "New Balance 327 Sea Salt/Navy",
    price: "M 1,340.00",
    image: "https://utfs.io/f/9WixNlVtj4JylTEhur0uvftgsVrhKXMqOd2RAYnwBpiPNcFL",
    badge: "Performance",
  },
  // Vans Checkerboard Stacked Platform - DISCOUNTED
  {
    id: "cmdk8s9fi000dkzgsj5yz6sx2",
    name: "Vans Checkerboard Stacked Platform - Black/White",
    price: "M 1,449.99",
    originalPrice: "M 1,950.00",
    image: "https://2iwp56ev9j.ufs.sh/f/9WixNlVtj4JyilhsAT7Pi8LQ3bZht5gPp4SUemFCJKrdMEwj",
    badge: "Sale",
  },
  // Lacoste Sport Tracksuit - DISCOUNTED
  {
    id: "cmdk8s7ed0001kzgss59b3moz",
    name: "Lacoste Sport Tracksuit - White/Navy",
    price: "M 2,000.00",
    originalPrice: "M 2,500.00",
    image: "https://2iwp56ev9j.ufs.sh/f/9WixNlVtj4JyP0JODZDjjG4Ze7ongTMfhmuK92XNzY8RHW53",
    badge: "Sale",
  },
];

const testimonials = [
  {
    name: "Thabo Mokoena",
    location: "Maseru",
    rating: 5,
    comment:
      "Exceptional quality and comfort. These sneakers exceeded my expectations!",
  },
  {
    name: "Nomsa Lebesa",
    location: "Mafeteng",
    rating: 5,
    comment:
      "Fast delivery and amazing customer service. Will definitely order again.",
  },
  {
    name: "Lerato Mthembu",
    location: "Leribe",
    rating: 5,
    comment: "Perfect fit and style. RIVV truly delivers premium quality.",
  },
];

export default function Home() {
  const { data: session, isPending } = useSession();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    const fetchUser = async () => {
      if (session?.user?.id) {
        try {
          const userResponse = await getUserById(session.user.id);
          if (userResponse.success && userResponse.data) {
            setUser(userResponse.data);
          }
        } catch (error) {
          console.error("Error fetching user:", error);
        }
      }
      setLoading(false);
    };

    if (!isPending) {
      fetchUser();
    }
  }, [session, isPending]);

  // Auto-rotate hero images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % 4);
    }, 4000); // Change image every 4 seconds

    return () => clearInterval(interval);
  }, []);

  // if (isPending || loading) {
  //   return <SpinnerCircle4 />;
  // }

  return (
    <div className="min-h-screen bg-white">
      {user ? (
        <NavBar
          user={user || (session?.user as User)}
          loading={isPending || loading}
        />
      ) : (
        <NavBarSkeleton loading={isPending || loading} user={null} />
      )}

      {/* Enhanced Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 py-12 sm:py-16 lg:py-32 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                >
                  {/* <Badge className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 hover:from-blue-200 hover:to-purple-200 border-0 px-4 py-2">
                    <Sparkles className="w-4 h-4 mr-2" />
                    AI-Enhanced Quality
                  </Badge> */}
                </motion.div>

                <motion.h1
                  className="text-3xl sm:text-4xl lg:text-6xl font-bold text-gray-900 leading-tight"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.8 }}
                >
                  Purposefully Curated.{" "}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Unapologetically Premium.
                  </span>
                </motion.h1>

                <motion.p
                  className="text-lg sm:text-xl text-gray-600 leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                >
                  RIVV Premium Sneakers delivers exceptional quality that speaks for itself.
                  Every pair is carefully selected for craftsmanship, comfort, and standout style.
                </motion.p>
              </div>

              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
              >
                <Button
                  asChild
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300"
                  title="Browse our full collection"
                  aria-label="Shop Collection"
                >
                  <Link href="/products">
                    Shop Collection <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-2 hover:bg-gray-50 transition-all duration-300"
                  title="Contact our team"
                  aria-label="Contact Us"
                >
                  <Link href="/contact">Contact Us</Link>
                </Button>
              </motion.div>

              {/* Enhanced Stats with Icons */}
              <motion.div
                className="grid grid-cols-3 gap-4 sm:gap-6 pt-8 border-t border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.6 }}
              >
                <div className="text-center group">
                  <div className="bg-blue-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2 group-hover:bg-blue-100 transition-colors">
                    <Box className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="text-xl sm:text-2xl font-bold text-gray-900">200+</div>
                  <div className="text-xs sm:text-sm text-gray-600">Premium Pairs</div>
                </div>
                <div className="text-center group">
                  <div className="bg-green-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2 group-hover:bg-green-100 transition-colors">
                    <Award className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="text-xl sm:text-2xl font-bold text-gray-900">100%</div>
                  <div className="text-xs sm:text-sm text-gray-600">Quality Score</div>
                </div>
                <div className="text-center group">
                  <div className="bg-purple-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2 group-hover:bg-purple-100 transition-colors">
                    <Truck className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="text-xl sm:text-2xl font-bold text-gray-900">24h</div>
                  <div className="text-xs sm:text-sm text-gray-600">Fast Delivery</div>
                </div>
              </motion.div>
            </motion.div>

            {/* Enhanced Auto-Rotating Image Showcase */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            >
              {/* Main Featured Product - Auto-rotating */}
              <div className="relative aspect-square rounded-3xl overflow-hidden mb-6 shadow-2xl">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentImageIndex}
                    initial={{ opacity: 0, scale: 1.1 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.8, ease: "easeInOut" }}
                    className="absolute inset-0"
                  >
                    <Image
                      src={featuredProducts[currentImageIndex].image}
                      alt={featuredProducts[currentImageIndex].name}
                      fill
                      className="object-cover"
                      priority
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

                    {/* Product Info Overlay */}
                    <div className="absolute bottom-6 left-6 right-6">
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3, duration: 0.6 }}
                        className="bg-white/95 backdrop-blur-sm rounded-2xl p-4"
                      >
                        <h3 className="font-bold text-gray-900 text-lg mb-1">
                          {featuredProducts[currentImageIndex].name}
                        </h3>
                        <div className="flex items-center gap-2">
                          <p className="text-2xl font-bold text-blue-600">
                            {featuredProducts[currentImageIndex].price}
                          </p>
                          {featuredProducts[currentImageIndex].originalPrice && (
                            <p className="text-lg text-gray-500 line-through">
                              {featuredProducts[currentImageIndex].originalPrice}
                            </p>
                          )}
                        </div>
                      </motion.div>
                    </div>
                  </motion.div>
                </AnimatePresence>

                {/* Rotation Indicators */}
                <div className="absolute bottom-4 right-4 flex gap-2">
                  {featuredProducts.slice(0, 4).map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={cn(
                        "w-3 h-3 rounded-full transition-all duration-300",
                        currentImageIndex === index
                          ? "bg-white shadow-lg"
                          : "bg-white/50 hover:bg-white/75"
                      )}
                      aria-label={`View product ${index + 1}`}
                    />
                  ))}
                </div>
              </div>

              {/* Thumbnail Grid */}
              <div className="grid grid-cols-4 gap-3">
                {featuredProducts.slice(0, 4).map((product, index) => (
                  <motion.div
                    key={product.id}
                    className={cn(
                      "relative aspect-square rounded-xl overflow-hidden cursor-pointer transition-all duration-300",
                      currentImageIndex === index
                        ? "ring-2 ring-blue-500 ring-offset-2"
                        : "hover:scale-105"
                    )}
                    onClick={() => setCurrentImageIndex(index)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                    <div className={cn(
                      "absolute inset-0 transition-opacity duration-300",
                      currentImageIndex === index
                        ? "bg-blue-500/20"
                        : "bg-black/0 hover:bg-black/10"
                    )} />
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Premium Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Premium Shopping Experience
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Advanced technology meets exceptional service for the ultimate sneaker shopping experience
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300 h-full">
                <Sparkles className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Premium Image Quality</h3>
                <p className="text-sm text-gray-600">Every product image optimized for perfect clarity and detail</p>
              </div>
            </motion.div>

            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300 h-full">
                <Shield className="w-8 h-8 text-green-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Order Accuracy</h3>
                <p className="text-sm text-gray-600">Advanced validation system ensures 100% order accuracy</p>
              </div>
            </motion.div>

            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300 h-full">
                <Zap className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Lightning Fast</h3>
                <p className="text-sm text-gray-600">Instant processing and seamless product browsing experience</p>
              </div>
            </motion.div>

            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300 h-full">
                <TrendingUp className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Quality Assurance</h3>
                <p className="text-sm text-gray-600">Comprehensive quality monitoring for every product</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services/Features Checklist Section - Only confirmed features */}
      <section className="py-12 bg-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl lg:text-3xl font-bold text-blue-900 mb-2">Why Shop With Us?</h2>
            <p className="text-lg text-blue-800">What makes RIVV different?</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Lay-Buy Payment Plan</span>
                <p className="text-blue-800 text-sm">Pay 60% upfront, the rest over 6 weeks. No interest, no stress!</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Free Delivery</span>
                <p className="text-blue-800 text-sm">Free in Maseru, and for orders over M3500 in other districts.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Secure Payments</span>
                <p className="text-blue-800 text-sm">Pay safely with M-Pesa, EcoCash, or Bank Transfer.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Premium Quality</span>
                <p className="text-blue-800 text-sm">Handpicked, authentic sneakers and fashionwear.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Trusted Sources</span>
                <p className="text-blue-800 text-sm">All products sourced from verified, trusted suppliers and presented to first-class standards.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Brands Checklist Section - Fashion-forward style */}
      <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              Premium <span className="text-blue-600">Brands</span>
            </h2>
            <p className="text-lg text-gray-700">Shop the world’s most iconic sneaker brands</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 lg:gap-6">
            {[
              { name: 'Air Jordan', accent: 'bg-red-500', textColor: 'text-red-600', bgColor: 'bg-red-50' },
              { name: 'Nike', accent: 'bg-orange-500', textColor: 'text-orange-600', bgColor: 'bg-orange-50' },
              { name: 'Adidas', accent: 'bg-gray-800', textColor: 'text-gray-700', bgColor: 'bg-gray-50' },
              { name: 'Timberland', accent: 'bg-yellow-600', textColor: 'text-yellow-700', bgColor: 'bg-yellow-50' },
              { name: 'New Balance', accent: 'bg-blue-600', textColor: 'text-blue-600', bgColor: 'bg-blue-50' },
              { name: 'Vans', accent: 'bg-purple-600', textColor: 'text-purple-600', bgColor: 'bg-purple-50' },
              { name: 'Converse', accent: 'bg-gray-900', textColor: 'text-gray-800', bgColor: 'bg-gray-100' },
              { name: 'Puma', accent: 'bg-green-600', textColor: 'text-green-600', bgColor: 'bg-green-50' },
            ].map((brand, index) => (
              <motion.div
                key={brand.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.05 }}
                className="group cursor-pointer"
              >
                <div className={`relative overflow-hidden rounded-2xl ${brand.bgColor} border border-white/50 shadow-lg hover:shadow-2xl transition-all duration-300 p-6 h-32 flex flex-col justify-between backdrop-blur-sm`}>
                  {/* Accent line */}
                  <div className={`absolute top-0 left-0 right-0 h-1 ${brand.accent} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300`}></div>

                  {/* Brand icon placeholder */}
                  <div className="flex justify-center mb-2">
                    <div className={`w-8 h-8 rounded-full ${brand.accent} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>
                  </div>

                  {/* Brand name */}
                  <div className="text-center">
                    <h3 className={`font-bold text-sm lg:text-base ${brand.textColor} group-hover:scale-105 transition-transform duration-300`}>
                      {brand.name}
                    </h3>
                  </div>

                  {/* Subtle background pattern */}
                  <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                    <div className="absolute top-4 right-4 w-16 h-16 rounded-full border-2 border-current"></div>
                    <div className="absolute bottom-4 left-4 w-8 h-8 rounded-full border border-current"></div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Call to action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <Link href="/products">
              <Button size="lg" className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                Explore All Brands
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
              Featured Collection
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover our handpicked selection of premium sneakers, each chosen
              for exceptional quality and style.
            </p>
          </div>

          {featuredProducts.length === 0 ? (
            <div className="text-center text-gray-500 py-12">No featured products available at the moment. Please check back soon!</div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredProducts.map((product) => (
                <Card
                  key={product.id}
                  className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
                >
                  <CardContent className="p-0">
                    <div className="relative aspect-square overflow-hidden rounded-t-lg">
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-white/90 text-gray-900 hover:bg-white">
                          {product.badge}
                        </Badge>
                      </div>
                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          size="sm"
                          variant="secondary"
                          className="rounded-full w-10 h-10 p-0"
                          title="Add to Wishlist"
                          aria-label="Add to Wishlist"
                          onClick={() => alert('Added to wishlist!')}
                        >
                          <Heart className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      <div>
                        <h3 className="font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                          {product.name}
                        </h3>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-2xl font-bold text-gray-900">
                            {product.price}
                          </span>
                          {product.originalPrice && (
                            <span className="text-lg text-gray-500 line-through">
                              {product.originalPrice}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <Button
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700"
                          asChild
                          title={`View ${product.name}`}
                          aria-label={`View ${product.name}`}
                        >
                          <Link href={`/products/${product.id}`}>
                            <Package className="h-4 w-4 mr-2" />
                            View Product
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Button asChild size="lg" variant="outline">
              <Link href="/products">
                View All Products <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Brand Story Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
                  We're Not Here to Meet Expectations—We're Here to Exceed Them
                </h2>
                <p className="text-lg text-gray-600 leading-relaxed">
                  RIVV Premium Sneakers is a proudly female-founded brand on a
                  mission to deliver quality that speaks for itself. Every pair
                  is carefully selected for its craftsmanship, comfort, and
                  standout style.
                </p>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Step in with confidence. You won't be disappointed.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-8">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Premium Quality
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Carefully curated for exceptional craftsmanship
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Comfort First
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Designed for all-day comfort and support
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Standout Style
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Unique designs that make a statement
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Female-Founded
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Proudly supporting women in business
                  </p>
                </div>
              </div>

              <Button asChild size="lg">
                <Link href="/contact">
                  Learn More About Us <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>

            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <div className="relative aspect-[4/5] rounded-2xl overflow-hidden">
                  <Image
                    src={featuredProducts[4].image}
                    alt="RIVV Premium Sneakers"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-[4/5] rounded-2xl overflow-hidden mt-8">
                  <Image
                    src={featuredProducts[5].image}
                    alt="RIVV Premium Sneakers"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
              What Our Customers Say
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it. Here's what our satisfied
              customers across Lesotho have to say.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className="p-6 hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-0 space-y-4">
                  <div className="flex items-center gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  <p className="text-gray-700 italic">
                    "{testimonial.comment}"
                  </p>
                  <div className="border-t pt-4">
                    <div className="font-semibold text-gray-900">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonial.location}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Mobile Floating Action Button */}
      <motion.div
        className="fixed bottom-6 right-6 z-50 lg:hidden"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 1, duration: 0.5 }}
      >
        <Button
          asChild
          size="lg"
          className="rounded-full w-14 h-14 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300"
          title="Shop Now"
          aria-label="Shop Collection"
        >
          <Link href="/products">
            <ShoppingBag className="h-6 w-6" />
          </Link>
        </Button>
      </motion.div>

      <Footer />
    </div>
  );
}
