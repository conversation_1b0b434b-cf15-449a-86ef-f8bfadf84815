import {
  sendTestCustomerOrderEmail,
  sendTestAdminOrderEmail,
  sendTestDelvaOrderEmail,
  sendTestPartnerReferralEmail
} from './lib/email-service';

async function runAllEmailTests() {
  console.log('--- Testing Customer Order Confirmation Email ---');
  await sendTestCustomerOrderEmail();

  console.log('--- Testing Admin Order Notification Email ---');
  await sendTestAdminOrderEmail();

  console.log('--- Testing Delva Delivery Notification Email ---');
  await sendTestDelvaOrderEmail();

  console.log('--- Testing Partner Referral Notification Email ---');
  await sendTestPartnerReferralEmail();

  console.log('--- All test emails attempted. Check your inboxes and logs for results. ---');
}

runAllEmailTests().catch((err) => {
  console.error('Error running email tests:', err);
}); 