// Legacy image AI utilities - DEPRECATED
// This file is maintained for backward compatibility only
// All image enhancement functionality has been removed

export type ImageEmbedding = number[];

export type ProductImage = {
  id: string;
  url: string;
  embedding?: ImageEmbedding;
  metadata: Record<string, any>;
};

export interface ImageAnalysis {
  score: number;
  issues: string[];
  suggestions: string[];
  estimatedQuality: 'low' | 'medium' | 'high';
}

export interface SimilarProduct {
  id: string;
  name: string;
  similarity: number;
  image: string;
}

/**
 * @deprecated Image processing functionality has been removed
 * Returns original image as fallback
 */
export async function processProductImages(params: {
  productName: string;
  brand: string;
  colorway: string;
  originalImageUrl: string;
}): Promise<{
  images: Array<{ angle: string; url: string }>;
  images_source: string;
  metadata: Record<string, any>;
}> {
  console.warn('processProductImages is deprecated - image enhancement system has been removed');
  // Return fallback with original image
  return {
    images: [{ angle: 'main', url: params.originalImageUrl }],
    images_source: 'original',
    metadata: {
      note: 'Image enhancement system has been removed',
      processed_at: new Date().toISOString()
    }
  };
}

/**
 * @deprecated Image analysis functionality has been removed
 */
export async function analyzeImageQuality(_imageUrl: string): Promise<ImageAnalysis> {
  console.warn('analyzeImageQuality is deprecated - image enhancement system has been removed');
  return {
    score: 0.5,
    issues: ['Image enhancement system has been removed'],
    suggestions: ['Use original Gemini analysis instead'],
    estimatedQuality: 'medium'
  };
}

/**
 * @deprecated Image defect detection functionality has been removed
 */
export async function detectImageDefects(_imageUrl: string): Promise<string[]> {
  console.warn('detectImageDefects is deprecated - image enhancement system has been removed');
  return [];
}

/**
 * @deprecated Similar product finding functionality has been removed
 */
export async function findSimilarProducts(_imageUrl: string): Promise<SimilarProduct[]> {
  console.warn('findSimilarProducts is deprecated - image enhancement system has been removed');
  return [];
}

/**
 * @deprecated Better image finding functionality has been removed
 */
export async function findBetterQualityImage(_productName: string, _currentImageUrl: string): Promise<string | null> {
  console.warn('findBetterQualityImage is deprecated - image enhancement system has been removed');
  return null;
}

/**
 * @deprecated Image optimization functionality has been removed
 */
export async function optimizeImage(imageUrl: string): Promise<string> {
  console.warn('optimizeImage is deprecated - image enhancement system has been removed');
  return imageUrl; // Return original URL as fallback
}

/**
 * @deprecated Image tag generation functionality has been removed
 */
export async function generateImageTags(_imageUrl: string): Promise<string[]> {
  console.warn('generateImageTags is deprecated - image enhancement system has been removed');
  return [];
}

/**
 * @deprecated Image enhancement functionality has been removed
 */
export async function enhanceImageQuality(imageUrl: string): Promise<string> {
  console.warn('enhanceImageQuality is deprecated - image enhancement system has been removed');
  return imageUrl;
}

/**
 * @deprecated Alternative image generation functionality has been removed
 */
export async function generateAlternativeImages(_productInfo: {
  name: string;
  brand: string;
  colorway: string;
  originalImageUrl: string;
}): Promise<string[]> {
  console.warn('generateAlternativeImages is deprecated - image enhancement system has been removed');
  return [];
}

/**
 * @deprecated Product info extraction functionality has been removed
 */
export async function extractProductInfo(_imageUrl: string): Promise<{
  brand?: string;
  model?: string;
  colorway?: string;
  category?: string;
  confidence: number;
}> {
  console.warn('extractProductInfo is deprecated - image enhancement system has been removed');
  return { confidence: 0 };
}

/**
 * @deprecated Image validation functionality has been removed
 */
export async function validateProductImage(_imageUrl: string, _productType: string): Promise<{
  isValid: boolean;
  issues: string[];
  score: number;
}> {
  console.warn('validateProductImage is deprecated - image enhancement system has been removed');
  return { isValid: false, issues: ['Image enhancement system has been removed'], score: 0 };
}

/**
 * @deprecated Image embedding functionality has been removed
 */
export async function generateImageEmbedding(_imageUrl: string): Promise<number[]> {
  console.warn('generateImageEmbedding is deprecated - image enhancement system has been removed');
  return [];
}
