import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth-utils';

export async function GET(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'You are not authorized to view this partner. Please sign in as an admin.' }, { status: 401 });
    }
    const { id: paramsId } = await context.params;
    const partner = await prisma.salesPartner.findUnique({
      where: { id: paramsId },
      include: {
        referralOrders: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });
    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }
    return NextResponse.json({ partner });
  } catch (error) {
    console.error('Error fetching partner:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Failed to fetch partner. Please try again or contact support.' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'You are not authorized to update this partner. Please sign in as an admin.' }, { status: 401 });
    }
    const { id: paramsId } = await context.params;
    const body = await request.json();
    const { name, surname, email, cellNumber, otherCellNumber, referralCode, discountAmount, discountCode, isActive } = body;
    // Validate required fields
    if (!name || !surname || !email || !cellNumber || !referralCode || !discountAmount || !discountCode) {
      return NextResponse.json({ error: 'Name, surname, email, cell number, referral code, discount amount, and discount code are required' }, { status: 400 });
    }

    // Check if referral code already exists (excluding current partner)
    const existingReferralCode = await prisma.salesPartner.findFirst({
      where: {
        referralCode: referralCode.trim(),
        id: { not: paramsId }
      }
    });
    if (existingReferralCode) {
      return NextResponse.json({ error: 'This referral code is already in use' }, { status: 400 });
    }

    // Check if discount code already exists (excluding current partner)
    const existingDiscountCode = await prisma.salesPartner.findFirst({
      where: {
        discountCode: discountCode.toUpperCase().trim(),
        id: { not: paramsId }
      }
    });
    if (existingDiscountCode) {
      return NextResponse.json({ error: 'This discount code is already in use' }, { status: 400 });
    }

    // Update partner
    const partner = await prisma.salesPartner.update({
      where: { id: paramsId },
      data: {
        name: name.trim(),
        surname: surname.trim(),
        email: email.toLowerCase().trim(),
        cellNumber: cellNumber.trim(),
        otherCellNumber: otherCellNumber?.trim() || null,
        referralCode: referralCode.trim(),
        discountAmount: parseFloat(discountAmount),
        discountCode: discountCode.toUpperCase().trim(),
        isActive: isActive,
      },
      include: {
        referralOrders: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });
    return NextResponse.json({ partner });
  } catch (error) {
    console.error('Error updating partner:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Failed to update partner. Please try again or contact support.' }, { status: 500 });
  }
} 