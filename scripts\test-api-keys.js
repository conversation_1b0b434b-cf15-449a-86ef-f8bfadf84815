// Quick script to test which Google AI API keys are working
const { GoogleGenerativeAI } = require("@google/generative-ai");

const API_KEYS = [
  { name: "GOOGLE_AI_API_KEY", key: "AIzaSyCBwFMaSZUHpOD_bPxz0pRWxEXaqABdBK0" },
  { name: "GOOGLE_AI_API_KEY_2", key: "AIzaSyATJfnR6GZas3xwlWlP1ASUzOAUaOcTiu0" },
  { name: "GOOGLE_AI_API_KEY_3", key: "AIzaSyDwpOW8mktMaF8E5bQomWKeEC1GpQNerfo" }
];

async function testAPIKey(keyInfo) {
  try {
    console.log(`\n🔑 Testing ${keyInfo.name}...`);
    
    const genAI = new GoogleGenerativeAI(keyInfo.key);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const result = await model.generateContent([
      "Just respond with 'Hello from <PERSON>!' - this is a test"
    ]);
    
    const response = await result.response;
    const text = response.text();
    
    console.log(`✅ ${keyInfo.name}: WORKING`);
    console.log(`   Response: ${text.substring(0, 50)}...`);
    return { name: keyInfo.name, status: 'WORKING', response: text };
    
  } catch (error) {
    console.log(`❌ ${keyInfo.name}: FAILED`);
    console.log(`   Error: ${error.message}`);
    
    let errorType = 'UNKNOWN';
    if (error.message.includes('403')) errorType = 'FORBIDDEN/BLOCKED';
    else if (error.message.includes('401')) errorType = 'UNAUTHORIZED/INVALID';
    else if (error.message.includes('429')) errorType = 'RATE_LIMITED';
    else if (error.message.includes('quota')) errorType = 'QUOTA_EXCEEDED';
    
    return { name: keyInfo.name, status: 'FAILED', errorType, error: error.message };
  }
}

async function main() {
  console.log('🧪 Testing Google AI API Keys...');
  console.log('==================================');
  
  const results = [];
  
  for (const keyInfo of API_KEYS) {
    const result = await testAPIKey(keyInfo);
    results.push(result);
    
    // Wait 2 seconds between tests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n📊 SUMMARY');
  console.log('===========');
  
  const working = results.filter(r => r.status === 'WORKING');
  const failed = results.filter(r => r.status === 'FAILED');
  
  console.log(`✅ Working Keys: ${working.length}/${results.length}`);
  working.forEach(r => console.log(`   - ${r.name}`));
  
  console.log(`❌ Failed Keys: ${failed.length}/${results.length}`);
  failed.forEach(r => console.log(`   - ${r.name}: ${r.errorType}`));
  
  if (working.length === 0) {
    console.log('\n🚨 NO WORKING API KEYS FOUND!');
    console.log('💡 Suggestions:');
    console.log('   1. Check that the Generative AI service is enabled in Google Cloud Console');
    console.log('   2. Verify the API keys are correct and not expired');
    console.log('   3. Make sure you have quota available');
    console.log('   4. Create new API keys if needed');
  } else {
    console.log(`\n🎉 ${working.length} API key(s) are working! You can proceed with image analysis.`);
  }
}

main().catch(console.error);
