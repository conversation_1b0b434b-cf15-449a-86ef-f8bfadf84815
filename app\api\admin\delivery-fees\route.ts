import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { ApiResponse } from "@/utils/types";

// GET /api/admin/delivery-fees - Get delivery fee data
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    // Get all delivered orders with delivery fees
    const deliveredOrders = await prisma.order.findMany({
      where: {
        status: "DELIVERED",
        deliveryFee: { not: null },
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
        orderItems: {
          select: {
            quantity: true,
          },
        },
      },
      orderBy: {
        deliveredAt: 'desc',
      },
    });

    // Format orders for display
    const formattedOrders = deliveredOrders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      customerName: order.user?.name || "Unknown Customer",
      deliveredAt: order.deliveredAt?.toISOString() || order.updatedAt.toISOString(),
      deliveryFee: order.deliveryFee || 0,
      isBulkDelivery: order.isBulkDelivery,
      shoesCount: order.orderItems.reduce((sum, item) => sum + item.quantity, 0),
      deliveryFeePaid: order.deliveryFeePaid,
      deliveryFeePaidAt: order.deliveryFeePaidAt?.toISOString() || null,
    }));

    // Calculate statistics
    const stats = {
      totalFeesOwed: deliveredOrders.reduce((sum, order) => sum + (order.deliveryFee || 0), 0),
      totalFeesPaid: deliveredOrders
        .filter(order => order.deliveryFeePaid)
        .reduce((sum, order) => sum + (order.deliveryFee || 0), 0),
      pendingFees: deliveredOrders
        .filter(order => !order.deliveryFeePaid)
        .reduce((sum, order) => sum + (order.deliveryFee || 0), 0),
      deliveredOrdersCount: deliveredOrders.length,
      bulkDeliveries: deliveredOrders.filter(order => order.isBulkDelivery).length,
      regularDeliveries: deliveredOrders.filter(order => !order.isBulkDelivery).length,
    };

    const response: ApiResponse<{ orders: typeof formattedOrders; stats: typeof stats }> = {
      success: true,
      data: {
        orders: formattedOrders,
        stats,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching delivery fees:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch delivery fees" },
      { status: 500 }
    );
  }
}
