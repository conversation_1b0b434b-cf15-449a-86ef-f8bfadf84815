import { Metadata } from 'next';
import { BulkDiscountForm } from '@/components/admin/products/bulk-discount/bulk-discount-form';
import { DiscountHistory } from '@/components/admin/products/bulk-discount/discount-history';

export const metadata: Metadata = {
  title: 'Bulk Discounts',
  description: 'Apply bulk discounts to products',
};

export default function BulkDiscountsPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">Bulk Discounts</h1>
          <p className="text-muted-foreground">
            Apply discounts to multiple products at once
          </p>
        </div>
      </div>

      <div className="grid gap-6">
        <BulkDiscountForm />
        <DiscountHistory />
      </div>
    </div>
  );
}
