import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { getLoyaltyDiscountStatus } from "@/lib/loyalty-system";

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get loyalty discount status for the current user
    const loyaltyStatus = await getLoyaltyDiscountStatus(user.id);

    return NextResponse.json({
      success: true,
      data: loyaltyStatus
    });

  } catch (error) {
    console.error('Error fetching loyalty status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
