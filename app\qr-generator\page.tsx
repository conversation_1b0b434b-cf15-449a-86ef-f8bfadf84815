"use client";

import QRCode from "@/components/ui/qr-code";

export default function QRGeneratorPage() {
  const qrLink = typeof window !== "undefined"
    ? `${window.location.origin}/qr-welcome`
    : "https://rivvsneakers.shop/qr-welcome";

  return (
    <main className="min-h-screen flex flex-col items-center justify-center bg-black text-white px-4 py-8">
      <h1 className="text-2xl font-bold mb-4 text-center">Reusable QR Code for Rivv Welcome Page</h1>
      <p className="mb-6 text-center text-gray-300 max-w-xs">
        Scan or share this QR code to bring customers to the Rivv Sneakers welcome page. Download and use it for print, WhatsApp, or flyers. This QR code is always valid and reusable.
      </p>
      <div className="bg-white p-4 rounded-xl shadow-lg mb-4">
        <QRCode
          data={qrLink}
          size={220}
          options={{ color: { dark: "#000000", light: "#FFFFFF" }, errorCorrectionLevel: "H", margin: 4 }}
          showDownload={true}
          showRefresh={false}
          alt="Rivv Welcome QR Code"
        />
      </div>
      <div className="text-xs text-gray-400 mt-2 text-center">
        Link: <span className="break-all">{qrLink}</span>
      </div>
    </main>
  );
} 