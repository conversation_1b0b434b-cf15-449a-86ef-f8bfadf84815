"use client";

import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import { User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Settings, 
  Package, 
  Truck, 
  AlertCircle, 
  Save,
  DollarSign,
  Users,
  Info
} from "lucide-react";

interface FeeSettings {
  defaultShippingFee: number;
  defaultLateCollectionFee: number;
  defaultDeliveryFee: number;
  defaultBulkDeliveryFee: number;
  bulkDeliveryThreshold: number;
}

export default function AdminFeeSettingsPage() {
  return (
    <AdminRoute>
      <AdminFeeSettingsPageContent />
    </AdminRoute>
  );
}

function AdminFeeSettingsPageContent() {
  const { data: session } = useSession();
  const [userWithRole, setUserWithRole] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [settings, setSettings] = useState<FeeSettings>({
    defaultShippingFee: 100,
    defaultLateCollectionFee: 10,
    defaultDeliveryFee: 90,
    defaultBulkDeliveryFee: 60,
    bulkDeliveryThreshold: 5,
  });

  const user = session!.user;

  useEffect(() => {
    const getUserDetails = async () => {
      if (user) {
        try {
          const userResponse = await getUserById(user.id);
          if (userResponse.success && userResponse.data) {
            setUserWithRole(userResponse.data as User);
          }
        } catch (error) {
          console.error("Error fetching user details:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    getUserDetails();
  }, [user]);

  useEffect(() => {
    if (userWithRole) {
      fetchFeeSettings();
    }
  }, [userWithRole]);

  const fetchFeeSettings = async () => {
    try {
      const response = await fetch('/api/admin/fee-settings');
      if (!response.ok) throw new Error('Failed to fetch fee settings');
      
      const data = await response.json();

      if (data.success) {
        setSettings(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch settings');
      }
    } catch (error) {
      console.error('Error fetching fee settings:', error);
      setMessage({ type: 'error', text: 'Failed to load fee settings' });
    }
  };

  const handleInputChange = (field: keyof FeeSettings, value: string) => {
    const numValue = parseFloat(value) || 0;
    setSettings(prev => ({ ...prev, [field]: numValue }));
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/admin/fee-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });

      if (!response.ok) throw new Error('Failed to update fee settings');

      const data = await response.json();

      if (data.success) {
        setMessage({ type: 'success', text: 'Fee settings updated successfully!' });
      } else {
        throw new Error(data.error || 'Failed to update settings');
      }
    } catch (error) {
      console.error('Error updating fee settings:', error);
      setMessage({ type: 'error', text: 'Failed to update fee settings' });
    } finally {
      setSaving(false);
    }
  };

  if (isLoading || !userWithRole) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <AdminLayout user={userWithRole}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Fee Settings</h1>
          <p className="text-gray-600">
            Manage default fee values for shipping, delivery, and other business costs.
          </p>
        </div>

        {message && (
          <Alert className={`mb-6 ${message.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>
            <AlertDescription className={message.type === 'success' ? 'text-green-700' : 'text-red-700'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Fee Settings Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Default Fee Values
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="defaultShippingFee" className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Default Shipping Fee (M per shoe)
                  </Label>
                  <Input
                    id="defaultShippingFee"
                    type="number"
                    step="0.01"
                    min="0"
                    value={settings.defaultShippingFee}
                    onChange={(e) => handleInputChange('defaultShippingFee', e.target.value)}
                    placeholder="100"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Default shipping fee charged by supplier per shoe
                  </p>
                </div>

                <div>
                  <Label htmlFor="defaultLateCollectionFee" className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    Default Late Collection Fee (M)
                  </Label>
                  <Input
                    id="defaultLateCollectionFee"
                    type="number"
                    step="0.01"
                    min="0"
                    value={settings.defaultLateCollectionFee}
                    onChange={(e) => handleInputChange('defaultLateCollectionFee', e.target.value)}
                    placeholder="10"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Default late collection fee if stock is not collected on time
                  </p>
                </div>

                <div>
                  <Label htmlFor="defaultDeliveryFee" className="flex items-center gap-2">
                    <Truck className="h-4 w-4" />
                    Default Delivery Fee (M per shoe)
                  </Label>
                  <Input
                    id="defaultDeliveryFee"
                    type="number"
                    step="0.01"
                    min="0"
                    value={settings.defaultDeliveryFee}
                    onChange={(e) => handleInputChange('defaultDeliveryFee', e.target.value)}
                    placeholder="90"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Default delivery fee charged to customers per shoe
                  </p>
                </div>

                <div>
                  <Label htmlFor="defaultBulkDeliveryFee" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Bulk Delivery Fee (M per shoe)
                  </Label>
                  <Input
                    id="defaultBulkDeliveryFee"
                    type="number"
                    step="0.01"
                    min="0"
                    value={settings.defaultBulkDeliveryFee}
                    onChange={(e) => handleInputChange('defaultBulkDeliveryFee', e.target.value)}
                    placeholder="60"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Reduced delivery fee for bulk orders (5+ shoes)
                  </p>
                </div>

                <div>
                  <Label htmlFor="bulkDeliveryThreshold" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Bulk Delivery Threshold (shoes)
                  </Label>
                  <Input
                    id="bulkDeliveryThreshold"
                    type="number"
                    min="1"
                    value={settings.bulkDeliveryThreshold}
                    onChange={(e) => handleInputChange('bulkDeliveryThreshold', e.target.value)}
                    placeholder="5"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Minimum number of shoes to qualify for bulk delivery discount
                  </p>
                </div>
              </div>

              <Button 
                onClick={handleSaveSettings} 
                disabled={saving}
                className="w-full"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Settings
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Information Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                How These Settings Work
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-1">Shipping Fee</h4>
                  <p className="text-sm text-blue-700">
                    This is the fee you pay to your supplier for each shoe. It's automatically applied when you enter cost prices for products.
                  </p>
                </div>

                <div className="p-3 bg-yellow-50 rounded-lg">
                  <h4 className="font-medium text-yellow-900 mb-1">Late Collection Fee</h4>
                  <p className="text-sm text-yellow-700">
                    Optional fee charged when you fail to collect stock on time. You can toggle this on/off per product during cost price entry.
                  </p>
                </div>

                <div className="p-3 bg-green-50 rounded-lg">
                  <h4 className="font-medium text-green-900 mb-1">Delivery Fee</h4>
                  <p className="text-sm text-green-700">
                    Fee charged to customers for delivery. Automatically calculated based on order size and bulk delivery rules.
                  </p>
                </div>

                <div className="p-3 bg-purple-50 rounded-lg">
                  <h4 className="font-medium text-purple-900 mb-1">Bulk Delivery</h4>
                  <p className="text-sm text-purple-700">
                    When {settings.bulkDeliveryThreshold}+ shoes are delivered together, the fee drops from M{settings.defaultDeliveryFee} to M{settings.defaultBulkDeliveryFee} per shoe.
                  </p>
                </div>

                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-1">Important Notes</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• These settings apply to new products and orders</li>
                    <li>• Existing products keep their current fee values</li>
                    <li>• You can override these defaults when entering cost prices</li>
                    <li>• Changes take effect immediately for new orders</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
} 