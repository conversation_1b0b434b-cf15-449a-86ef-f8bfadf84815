import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth-utils';
import { sendPartnerWelcomeEmail } from '@/lib/email-service';
import { randomBytes } from 'crypto';
import { UserRole } from '@prisma/client';
import crypto from 'crypto';
// import { hash } from 'bcryptjs'; // REMOVE THIS

// Generate a unique referral code
function generateReferralCode(name: string, surname: string): string {
  const base = (name.trim().slice(0, 4) + surname.trim().slice(0, 2)).toUpperCase();
  const random = Math.floor(10 + Math.random() * 90);
  return `${base}${random}`;
}

// POST: Create a new partner
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'You are not authorized to perform this action. Please sign in as an admin.' }, { status: 401 });
    }

    const body = await request.json();
    const { name, surname, email, cellNumber, otherCellNumber, referralCode, discountAmount, discountCode } = body;

    // Validate required fields
    if (!name || !surname || !email || !cellNumber || !discountAmount || !discountCode) {
      return NextResponse.json(
        { error: 'Name, surname, email, cell number, discount amount, and discount code are required' },
        { status: 400 }
      );
    }

    // Check if email already exists as a user
    let partnerUser = await prisma.user.findUnique({ where: { email: email.toLowerCase() } });
    if (partnerUser) {
      // Update user to have isPartner flag
      partnerUser = await prisma.user.update({
        where: { id: partnerUser.id },
        data: {
          role: UserRole.USER, // Set to USER (or omit if default)
          isPartner: true,
          emailVerified: true, // Auto-verify sales partners
        },
      });
    } else {
      // Create the user account for the partner
      partnerUser = await prisma.user.create({
        data: {
          id: crypto.randomUUID(),
          name: `${name.trim()} ${surname.trim()}`,
          email: email.toLowerCase().trim(),
          emailVerified: true, // Auto-verify sales partners
          role: UserRole.USER, // Set to USER (or omit if default)
          isPartner: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          // Remove accounts.create block for compatibility
        },
      });
    }

    // Check if email already exists as a partner
    const existingEmail = await prisma.salesPartner.findUnique({ where: { email: email.toLowerCase() } });
    if (existingEmail) {
      return NextResponse.json(
        { error: 'A partner with this email already exists' },
        { status: 400 }
      );
    }

    // Generate or validate referral code
    let finalReferralCode = referralCode;
    if (!finalReferralCode) {
      finalReferralCode = generateReferralCode(name, surname);
    }

    // Check if referral code already exists
    const existingCode = await prisma.salesPartner.findUnique({ where: { referralCode: finalReferralCode } });
    if (existingCode) {
      return NextResponse.json(
        { error: 'This referral code is already in use' },
        { status: 400 }
      );
    }

    // Check if discount code already exists
    const existingDiscountCode = await prisma.salesPartner.findUnique({ where: { discountCode: discountCode.toUpperCase() } });
    if (existingDiscountCode) {
      return NextResponse.json(
        { error: 'This discount code is already in use' },
        { status: 400 }
      );
    }

    // Generate a secure random password for the partner
    const rawPassword = randomBytes(8).toString('base64');
    // const hashedPassword = await hash(rawPassword, 10); // REMOVE THIS

    // Create the partner and link to user
    const partner = await prisma.salesPartner.create({
      data: {
        name: name.trim(),
        surname: surname.trim(),
        email: email.toLowerCase().trim(),
        cellNumber: cellNumber.trim(),
        otherCellNumber: otherCellNumber?.trim() || null,
        referralCode: finalReferralCode,
        discountAmount: parseFloat(discountAmount),
        discountCode: discountCode.toUpperCase().trim(),
        isActive: true,
        commissionEarned: 0,
        bonusPaid: 0,
        userId: partnerUser.id,
      },
    });

    // Send welcome email with dashboard link and password reset instructions
    const dashboardUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/profile/referrals`;
    sendPartnerWelcomeEmail({
      name: partner.name,
      email: partner.email,
      dashboardUrl,
      // Removed showPasswordReset property
    }).catch(e => console.error('Failed to send partner welcome email:', e));

    return NextResponse.json({ 
      success: true, 
      partner,
      message: 'Partner created successfully' 
    });

  } catch (error) {
    console.error('Error creating partner:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create partner. Please try again or contact support.' },
      { status: 500 }
    );
  }
}

// GET: List all partners
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'You are not authorized to view partners. Please sign in as an admin.' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build where clause for search
    const where = search ? {
      OR: [
        { name: { contains: search } },
        { surname: { contains: search } },
        { email: { contains: search } },
        { referralCode: { contains: search } },
      ]
    } : {};

    // Get partners with pagination
    const [partners, total] = await Promise.all([
      prisma.salesPartner.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: { referralOrders: true }
          }
        }
      }),
      prisma.salesPartner.count({ where })
    ]);

    return NextResponse.json({
      partners,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching partners:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch partners. Please try again or contact support.' },
      { status: 500 }
    );
  }
} 