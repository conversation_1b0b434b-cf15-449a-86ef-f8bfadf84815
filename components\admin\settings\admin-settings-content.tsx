"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { 
  Settings,
  Globe,
  Mail,
  CreditCard,
  Bell,
  Shield,
  Database,
  Palette,
  Save,
  RefreshCw,
  DollarSign
} from "lucide-react";
import { useState, useEffect } from "react";

interface SettingsData {
  general: {
    siteName: string;
    siteDescription: string;
    siteUrl: string;
    contactEmail: string;
    supportEmail: string;
    currency: string;
    timezone: string;
    language: string;
  };
  email: {
    smtpHost: string;
    smtpPort: string;
    smtpUser: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
    enableNotifications: boolean;
  };
  payment: {
    enablePayments: boolean;
    paymentMethods: string[];
    taxRate: number;
    shippingFee: number;
    freeShippingThreshold: number;
  };
  notifications: {
    orderNotifications: boolean;
    userRegistrations: boolean;
    lowStockAlerts: boolean;
    systemUpdates: boolean;
    emailDigest: boolean;
  };
  security: {
    enableTwoFactor: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requireEmailVerification: boolean;
  };
  appearance: {
    theme: string;
    primaryColor: string;
    logoUrl: string;
    faviconUrl: string;
    customCss: string;
  };
  feeSettings: {
    defaultShippingFee: number;
    defaultLateCollectionFee: number;
    defaultDeliveryFee: number;
    bulkDeliveryFee: number;
  };
}

const defaultSettings: SettingsData = {
  general: {
    siteName: "Rivv E-commerce",
    siteDescription: "Premium footwear and fashion accessories",
    siteUrl: "https://rivv-ecommerce.com",
    contactEmail: "<EMAIL>",
    supportEmail: "<EMAIL>",
    currency: "M",
    timezone: "UTC",
    language: "en"
  },
  email: {
    smtpHost: "smtp.gmail.com",
    smtpPort: "587",
    smtpUser: "<EMAIL>",
    smtpPassword: "••••••••",
    fromEmail: "<EMAIL>",
    fromName: "Rivv E-commerce",
    enableNotifications: true
  },
  payment: {
    enablePayments: true,
    paymentMethods: ["credit_card", "paypal", "bank_transfer"],
    taxRate: 8.5,
    shippingFee: 15.00,
    freeShippingThreshold: 100.00
  },
  notifications: {
    orderNotifications: true,
    userRegistrations: true,
    lowStockAlerts: true,
    systemUpdates: false,
    emailDigest: true
  },
  security: {
    enableTwoFactor: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    requireEmailVerification: true
  },
  appearance: {
    theme: "light",
    primaryColor: "#3b82f6",
    logoUrl: "",
    faviconUrl: "",
    customCss: ""
  },
  feeSettings: {
    defaultShippingFee: 100,
    defaultLateCollectionFee: 10,
    defaultDeliveryFee: 90,
    bulkDeliveryFee: 60,
  }
};

export default function AdminSettingsContent() {
  const [settings, setSettings] = useState<SettingsData>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Fetch settings from API on mount
  useEffect(() => {
    async function fetchSettings() {
      setIsLoading(true);
      try {
        const res = await fetch("/api/admin/settings");
        const result = await res.json();
        if (result.success && result.data) {
          setSettings((prev) => ({
            ...prev,
            appearance: {
              ...prev.appearance,
              primaryColor: result.data.primaryColor || prev.appearance.primaryColor,
            },
            feeSettings: result.data.feeSettings || prev.feeSettings,
          }));
        }
      } finally {
        setIsLoading(false);
      }
    }
    fetchSettings();
  }, []);

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      // Save primary color to API
      await fetch("/api/admin/settings", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ primaryColor: settings.appearance.primaryColor }),
      });
      alert("Settings saved successfully!");
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("Failed to save settings");
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (section: keyof SettingsData, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-2">Configure your application settings and preferences</p>
        </div>
        <Button onClick={handleSaveSettings} disabled={isSaving}>
          <Save className="h-4 w-4 mr-2" />
          {isSaving ? "Saving..." : "Save All Changes"}
        </Button>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            General Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="siteName">Site Name</Label>
              <Input
                id="siteName"
                value={settings.general.siteName}
                onChange={(e) => updateSetting('general', 'siteName', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="siteUrl">Site URL</Label>
              <Input
                id="siteUrl"
                value={settings.general.siteUrl}
                onChange={(e) => updateSetting('general', 'siteUrl', e.target.value)}
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="siteDescription">Site Description</Label>
              <Textarea
                id="siteDescription"
                value={settings.general.siteDescription}
                onChange={(e) => updateSetting('general', 'siteDescription', e.target.value)}
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="contactEmail">Contact Email</Label>
              <Input
                id="contactEmail"
                type="email"
                value={settings.general.contactEmail}
                onChange={(e) => updateSetting('general', 'contactEmail', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="supportEmail">Support Email</Label>
              <Input
                id="supportEmail"
                type="email"
                value={settings.general.supportEmail}
                onChange={(e) => updateSetting('general', 'supportEmail', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="currency">Currency Symbol</Label>
              <Input
                id="currency"
                value={settings.general.currency}
                onChange={(e) => updateSetting('general', 'currency', e.target.value)}
                placeholder="M"
              />
            </div>
            <div>
              <Label htmlFor="timezone">Timezone</Label>
              <Select 
                value={settings.general.timezone} 
                onValueChange={(value) => updateSetting('general', 'timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="America/New_York">Eastern Time</SelectItem>
                  <SelectItem value="America/Chicago">Central Time</SelectItem>
                  <SelectItem value="America/Denver">Mountain Time</SelectItem>
                  <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Email Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="enableNotifications"
              checked={settings.email.enableNotifications}
              onCheckedChange={(checked) => updateSetting('email', 'enableNotifications', checked)}
            />
            <Label htmlFor="enableNotifications">Enable Email Notifications</Label>
          </div>
          
          <Separator />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="smtpHost">SMTP Host</Label>
              <Input
                id="smtpHost"
                value={settings.email.smtpHost}
                onChange={(e) => updateSetting('email', 'smtpHost', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="smtpPort">SMTP Port</Label>
              <Input
                id="smtpPort"
                value={settings.email.smtpPort}
                onChange={(e) => updateSetting('email', 'smtpPort', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="fromEmail">From Email</Label>
              <Input
                id="fromEmail"
                type="email"
                value={settings.email.fromEmail}
                onChange={(e) => updateSetting('email', 'fromEmail', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="fromName">From Name</Label>
              <Input
                id="fromName"
                value={settings.email.fromName}
                onChange={(e) => updateSetting('email', 'fromName', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="enablePayments"
              checked={settings.payment.enablePayments}
              onCheckedChange={(checked) => updateSetting('payment', 'enablePayments', checked)}
            />
            <Label htmlFor="enablePayments">Enable Online Payments</Label>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="taxRate">Tax Rate (%)</Label>
              <Input
                id="taxRate"
                type="number"
                step="0.1"
                value={settings.payment.taxRate}
                onChange={(e) => updateSetting('payment', 'taxRate', parseFloat(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="shippingFee">Shipping Fee (M)</Label>
              <Input
                id="shippingFee"
                type="number"
                step="0.01"
                value={settings.payment.shippingFee}
                onChange={(e) => updateSetting('payment', 'shippingFee', parseFloat(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="freeShippingThreshold">Free Shipping Threshold (M)</Label>
              <Input
                id="freeShippingThreshold"
                type="number"
                step="0.01"
                value={settings.payment.freeShippingThreshold}
                onChange={(e) => updateSetting('payment', 'freeShippingThreshold', parseFloat(e.target.value))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="orderNotifications">Order Notifications</Label>
                <p className="text-sm text-gray-600">Receive notifications for new orders</p>
              </div>
              <Switch
                id="orderNotifications"
                checked={settings.notifications.orderNotifications}
                onCheckedChange={(checked) => updateSetting('notifications', 'orderNotifications', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="userRegistrations">User Registrations</Label>
                <p className="text-sm text-gray-600">Receive notifications for new user registrations</p>
              </div>
              <Switch
                id="userRegistrations"
                checked={settings.notifications.userRegistrations}
                onCheckedChange={(checked) => updateSetting('notifications', 'userRegistrations', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="lowStockAlerts">Low Stock Alerts</Label>
                <p className="text-sm text-gray-600">Receive alerts when products are low in stock</p>
              </div>
              <Switch
                id="lowStockAlerts"
                checked={settings.notifications.lowStockAlerts}
                onCheckedChange={(checked) => updateSetting('notifications', 'lowStockAlerts', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailDigest">Daily Email Digest</Label>
                <p className="text-sm text-gray-600">Receive daily summary of activities</p>
              </div>
              <Switch
                id="emailDigest"
                checked={settings.notifications.emailDigest}
                onCheckedChange={(checked) => updateSetting('notifications', 'emailDigest', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                value={settings.security.sessionTimeout}
                onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
              <Input
                id="maxLoginAttempts"
                type="number"
                value={settings.security.maxLoginAttempts}
                onChange={(e) => updateSetting('security', 'maxLoginAttempts', parseInt(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
              <Input
                id="passwordMinLength"
                type="number"
                value={settings.security.passwordMinLength}
                onChange={(e) => updateSetting('security', 'passwordMinLength', parseInt(e.target.value))}
              />
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Switch
                id="requireEmailVerification"
                checked={settings.security.requireEmailVerification}
                onCheckedChange={(checked) => updateSetting('security', 'requireEmailVerification', checked)}
              />
              <Label htmlFor="requireEmailVerification">Require Email Verification</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="enableTwoFactor"
                checked={settings.security.enableTwoFactor}
                onCheckedChange={(checked) => updateSetting('security', 'enableTwoFactor', checked)}
              />
              <Label htmlFor="enableTwoFactor">Enable Two-Factor Authentication</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Appearance Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Appearance Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="theme">Theme</Label>
              <Select 
                value={settings.appearance.theme} 
                onValueChange={(value) => updateSetting('appearance', 'theme', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="auto">Auto</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="primaryColor">Primary Color</Label>
              <Input
                id="primaryColor"
                type="color"
                value={settings.appearance.primaryColor}
                onChange={(e) => updateSetting('appearance', 'primaryColor', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="logoUrl">Logo URL</Label>
              <Input
                id="logoUrl"
                value={settings.appearance.logoUrl}
                onChange={(e) => updateSetting('appearance', 'logoUrl', e.target.value)}
                placeholder="https://example.com/logo.png"
              />
            </div>
            <div>
              <Label htmlFor="faviconUrl">Favicon URL</Label>
              <Input
                id="faviconUrl"
                value={settings.appearance.faviconUrl}
                onChange={(e) => updateSetting('appearance', 'faviconUrl', e.target.value)}
                placeholder="https://example.com/favicon.ico"
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="customCss">Custom CSS</Label>
            <Textarea
              id="customCss"
              value={settings.appearance.customCss}
              onChange={(e) => updateSetting('appearance', 'customCss', e.target.value)}
              placeholder="/* Add your custom CSS here */"
              rows={6}
              className="font-mono text-sm"
            />
          </div>
        </CardContent>
      </Card>

      {/* Fee Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Fee Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="defaultShippingFee">Shipping Fee per Shoe (M)</Label>
              <Input
                id="defaultShippingFee"
                type="number"
                step="0.01"
                value={settings.feeSettings.defaultShippingFee}
                onChange={e => setSettings(prev => ({
                  ...prev,
                  feeSettings: {
                    ...prev.feeSettings,
                    defaultShippingFee: parseFloat(e.target.value),
                  }
                }))}
              />
            </div>
            <div>
              <Label htmlFor="defaultLateCollectionFee">Late Collection Fee (M)</Label>
              <Input
                id="defaultLateCollectionFee"
                type="number"
                step="0.01"
                value={settings.feeSettings.defaultLateCollectionFee}
                onChange={e => setSettings(prev => ({
                  ...prev,
                  feeSettings: {
                    ...prev.feeSettings,
                    defaultLateCollectionFee: parseFloat(e.target.value),
                  }
                }))}
              />
            </div>
            <div>
              <Label htmlFor="defaultDeliveryFee">Delivery Fee per Shoe (M)</Label>
              <Input
                id="defaultDeliveryFee"
                type="number"
                step="0.01"
                value={settings.feeSettings.defaultDeliveryFee}
                onChange={e => setSettings(prev => ({
                  ...prev,
                  feeSettings: {
                    ...prev.feeSettings,
                    defaultDeliveryFee: parseFloat(e.target.value),
                  }
                }))}
              />
            </div>
            <div>
              <Label htmlFor="bulkDeliveryFee">Bulk Delivery Discount Fee (M, 5+ shoes)</Label>
              <Input
                id="bulkDeliveryFee"
                type="number"
                step="0.01"
                value={settings.feeSettings.bulkDeliveryFee}
                onChange={e => setSettings(prev => ({
                  ...prev,
                  feeSettings: {
                    ...prev.feeSettings,
                    bulkDeliveryFee: parseFloat(e.target.value),
                  }
                }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSaveSettings} disabled={isSaving} size="lg">
          <Save className="h-4 w-4 mr-2" />
          {isSaving ? "Saving..." : "Save All Changes"}
        </Button>
      </div>
    </div>
  );
}
