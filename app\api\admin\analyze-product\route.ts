import { NextResponse } from 'next/server';
import { getProductDescriptionFromImage } from '@/utils/replicate'; // This function name is now misleading, but we'll keep it for now to avoid breaking changes in other parts of the app that are not in scope.

export async function POST(req: Request) {
  try {
    const { imageUrl, category, analysisType = 'product_description' } = await req.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    // Convert URL to base64
    const response = await fetch(imageUrl);

    if (!response.ok) {
      return NextResponse.json({
        error: 'Image access error',
        description: "We couldn't access the image. Please try again."
      }, { status: 400 });
    }

    const contentType = response.headers.get('content-type');
    if (!contentType?.startsWith('image/')) {
      return NextResponse.json({
        error: 'Invalid image',
        description: "The file is not a valid image."
      }, { status: 400 });
    }

    const arrayBuffer = await response.arrayBuffer();
    const base64Data = Buffer.from(arrayBuffer).toString('base64');
    const base64Image = `data:${contentType};base64,${base64Data}`;
    
    // Pass category and analysis type to Gemini analysis
    const description = await getProductDescriptionFromImage(base64Image, category, analysisType);

    if (!description) {
      return NextResponse.json({
        error: 'AI analysis failed',
        description: "We couldn't generate a description for this image. This might happen if the image is not clear or the quality is too low. Please try uploading a clear, well-lit photo of the entire product."
      }, { status: 400 });
    }

    // Handle different response types
    if (analysisType === 'quality_and_angles') {
      try {
        const parsed = JSON.parse(description);
        return NextResponse.json({
          success: true,
          analysisType: 'quality_and_angles',
          data: parsed
        });
      } catch (parseError) {
        return NextResponse.json({
          success: true,
          analysisType: 'quality_and_angles',
          data: { rawResponse: description }
        });
      }
    } else {
      // Return the structured JSON directly for product descriptions
      try {
        return NextResponse.json(JSON.parse(description));
      } catch (parseError) {
        return NextResponse.json({
          success: false,
          error: 'Invalid JSON response from AI',
          rawResponse: description
        });
      }
    }

  } catch (error) {
    console.error('Error analyzing product:', error);
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json({
          error: 'API configuration error',
          description: "The Gemini API key appears to be invalid. Please check your API key configuration."
        }, { status: 500 });
      }
      
      if (error.message.includes('quota') || error.message.includes('rate limit')) {
        return NextResponse.json({
          error: 'API quota exceeded',
          description: "The Gemini API quota has been exceeded. Please try again later or contact support."
        }, { status: 429 });
      }
    }
    return NextResponse.json({
      error: 'Unexpected error',
      description: "Something went wrong while analyzing the image. Please try again."
    }, { status: 500 });
  }
} 