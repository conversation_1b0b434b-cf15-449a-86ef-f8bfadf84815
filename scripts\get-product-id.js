const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function getProductId() {
  try {
    const product = await prisma.product.findFirst({
      where: { 
        isActive: true, 
        images: { isEmpty: false } 
      },
      select: { 
        id: true, 
        name: true 
      }
    });
    
    if (product) {
      console.log('Product ID:', product.id);
      console.log('Name:', product.name);
    } else {
      console.log('No products found');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getProductId();
