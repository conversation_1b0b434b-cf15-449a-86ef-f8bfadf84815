import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import {
  sendOrderConfirmationEmail,
  sendAdminOrderNotification,
  sendDelvaDeliveryNotification,
  sendPartnerReferralNotification
} from "@/lib/email-service";
import { ApiResponse } from "@/utils/types";
import { calculateDeliveryFee as calculateDistrictBasedDeliveryFee } from "@/lib/product-utils";
import { validateOrder } from "@/lib/order-validation";

// Generate unique order number
function generateOrderNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `ORD-${timestamp.slice(-6)}${random}`;
}

// GET /api/orders - Get user's orders
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");

    const where: any = { userId: user.id };
    if (status) {
      where.status = status;
    }

    const skip = (page - 1) * limit;
    const total = await prisma.order.count({ where });

    const orders = await prisma.order.findMany({
      where,
      include: {
        orderItems: {
          include: {
            product: {
              include: {
                category: true,
              },
            },
          },
        },
        discountCode: true,
        paymentProof: true,
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    });

    const response: ApiResponse = {
      success: true,
      data: {
        data: orders,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch orders" },
      { status: 500 }
    );
  }
}

// POST /api/orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      shippingAddress,
      phoneNumber,
      notes,
      discountAmount: rawDiscountAmount = 0,
      discountCode,
      paymentMethod,
      paymentProofUrl,
      deliveryFee: providedDeliveryFee,
      items,
    } = body;

    // Always use positive discount amount
    let discountAmount = Math.abs(rawDiscountAmount);

    // Validate required fields
    if (!shippingAddress || !phoneNumber || !items || items.length === 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Calculate total amount and fees
    let totalAmount = 0;
    let totalShoes = 0;
    const orderItems: any[] = [];

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId, isActive: true },
      });

      if (!product) {
        return NextResponse.json(
          { success: false, error: `Product ${item.productId} not found` },
          { status: 400 }
        );
      }

      if (product.stock < item.quantity) {
        return NextResponse.json(
          { success: false, error: `Insufficient stock for ${product.name}` },
          { status: 400 }
        );
      }

      const itemTotal = item.price * item.quantity;
      totalAmount += itemTotal;
      totalShoes += item.quantity;

      // Use all cost and fee fields from product, fallback to 0 if missing
      const costPrice = (product as any).costPrice ?? 0;
      const shippingFee = (product as any).shippingFee ?? 0;
      const lateCollectionFee = (product as any).lateCollectionFee ?? 0;
      const totalCost = (costPrice + shippingFee + lateCollectionFee) * item.quantity;
      const profit = (item.price - (costPrice + shippingFee + lateCollectionFee)) * item.quantity;

      orderItems.push({
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        costPrice: costPrice,
        shippingFee: shippingFee,
        lateCollectionFee: lateCollectionFee,
        totalCost: totalCost,
        profit: profit,
        size: item.size,
        color: item.color,
      });
    }

    // Use provided delivery fee from frontend (calculated based on district)
    // If not provided, fall back to district-based calculation
    let deliveryFee = providedDeliveryFee || 0;
    let isBulkDelivery = false;

    if (deliveryFee === undefined || deliveryFee === null) {
      // Extract district from shipping address for fallback calculation
      const addressParts = shippingAddress.split(', ');
      const district = addressParts.length >= 3 ? addressParts[2] : 'Maseru';
      const subtotalForDelivery = totalAmount - discountAmount;
      const deliveryInfo = calculateDistrictBasedDeliveryFee(district, subtotalForDelivery);
      deliveryFee = deliveryInfo.fee;
    }

    // Calculate total cost and profit
    const totalCostPrice = orderItems.reduce((sum, item) => sum + (item.totalCost || 0), 0);
    const totalProfit = orderItems.reduce((sum, item) => sum + (item.profit || 0), 0);

    // Only set totals if all items have cost prices
    const allItemsHaveCostPrice = orderItems.every(item => item.costPrice !== null);
    const finalTotalCostPrice = allItemsHaveCostPrice ? totalCostPrice : null;
    const finalTotalProfit = allItemsHaveCostPrice ? totalProfit : null;

    // Final total amount
    totalAmount = totalAmount - discountAmount + deliveryFee;

    // Validate order calculations before proceeding
    const orderValidationData = {
      items: items.map((item: any) => ({
        productId: item.productId,
        quantity: item.quantity,
        price: item.price
      })),
      discountCode,
      discountAmount,
      deliveryFee,
      subtotal: totalAmount + discountAmount - deliveryFee,
      total: totalAmount,
      customerId: user.id
    };

    const validation = await validateOrder(orderValidationData);

    // Log validation warnings for admin review
    if (validation.warnings.length > 0) {
      console.warn('Order validation warnings:', validation.warnings);
    }

    // Auto-correct minor discrepancies
    if (!validation.isValid && validation.discrepancy > 0.01) {
      console.log('Order validation corrections applied:', validation.corrections);

      // Apply corrections to the order
      if (validation.corrections.some(c => c.field === 'discountAmount')) {
        const correctedDiscount = validation.corrections.find(c => c.field === 'discountAmount');
        if (correctedDiscount) {
          discountAmount = correctedDiscount.correctedValue;
        }
      }

      if (validation.corrections.some(c => c.field === 'deliveryFee')) {
        const correctedDelivery = validation.corrections.find(c => c.field === 'deliveryFee');
        if (correctedDelivery) {
          deliveryFee = correctedDelivery.correctedValue;
        }
      }

      // Recalculate total with corrections
      totalAmount = (totalAmount + discountAmount - deliveryFee) - discountAmount + deliveryFee;
    }

    // Validate discount code if provided
    let discountCodeId = null;
    let partner = null;
    if (discountCode) {
      // First, try to find a regular discount code
      const discount = await prisma.discountCode.findUnique({
        where: { code: discountCode, isActive: true },
      });

      if (discount) {
        // Check if discount code is not yet valid
        if (discount.validFrom && new Date() < discount.validFrom) {
          return NextResponse.json(
            { success: false, error: "Discount code is not yet active" },
            { status: 400 }
          );
        }

        // Check if discount is still valid
        if (discount.validUntil && discount.validUntil < new Date()) {
          return NextResponse.json(
            { success: false, error: "Discount code has expired" },
            { status: 400 }
          );
        }

        // Check usage limits
        if (discount.maxUses && discount.usedCount >= discount.maxUses) {
          return NextResponse.json(
            { success: false, error: "Discount code usage limit reached" },
            { status: 400 }
          );
        }

        discountCodeId = discount.id;
      } else {
        // If not found as discount code, check if it's a partner referral code
        partner = await prisma.salesPartner.findUnique({
          where: { referralCode: discountCode, isActive: true },
        });

        if (!partner) {
          return NextResponse.json(
            { success: false, error: "Invalid discount code" },
            { status: 400 }
          );
        }
      }
    }

    // Create order in transaction with increased timeout
    const result = await prisma.$transaction(async (tx) => {
      // Generate unique order number
      const orderNumber = generateOrderNumber();

      // Clear user's cart first (fastest operation)
      await tx.cartItem.deleteMany({
        where: { userId: user.id },
      });

      // Update product stock in batch
      const stockUpdates = items.map((item: any) =>
        tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        })
      );
      await Promise.all(stockUpdates);

      // Update discount code usage if applicable
      if (discountCodeId) {
        await tx.discountCode.update({
          where: { id: discountCodeId },
          data: {
            usedCount: {
              increment: 1,
            },
          },
        });
      }

      // Create order with items
      const order = await tx.order.create({
        data: {
          userId: user.id,
          orderNumber: orderNumber,
          status: "PENDING",
          shippingAddress,
          phoneNumber,
          notes,
          totalAmount,
          discountAmount,
          discountCodeId,
          deliveryFee,
          isBulkDelivery,
          totalCostPrice: finalTotalCostPrice,
          totalProfit: finalTotalProfit,
          orderItems: {
            create: orderItems,
          },
        },
        include: {
          orderItems: true,
          discountCode: true,
          paymentProof: true,
        },
      });

      // Create payment proof if provided
      if (paymentProofUrl) {
        await tx.paymentProof.create({
          data: {
            orderId: order.id,
            imageUrl: paymentProofUrl,
            status: "PENDING",
            notes: paymentMethod ? `Payment Method: ${paymentMethod}` : undefined,
          },
        });
      }

      return order;
    }, {
      maxWait: 10000, // 10 seconds max wait
      timeout: 15000, // 15 seconds timeout
    });

    // Refetch the order with all necessary nested includes for email templates
    const orderWithFullProduct: any = await prisma.order.findUnique({
      where: { id: result.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        orderItems: {
          include: {
            product: {
              include: {
                category: true,
              },
            },
          },
        },
        discountCode: true,
        paymentProof: true,
      },
    });

    // --- Referral System Logic ---
    if (discountCode && partner) {
      // Calculate commission: 5% of order value before discount and delivery
      const orderValue = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const commission = Math.round(orderValue * 0.05 * 100) / 100;
      // Create ReferralOrder
      await prisma.referralOrder.create({
        data: {
          orderId: result.id,
          partnerId: partner.id,
          customerName: orderWithFullProduct.user?.name || '',
          orderValue: orderValue,
          commission: commission,
        }
      });
      // Update partner's commission earned
      await prisma.salesPartner.update({
        where: { id: partner.id },
        data: {
          commissionEarned: { increment: commission },
        }
      });
    }
    // --- End Referral System Logic ---

    // Send emails asynchronously (don't block the response)
    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
    const customerEmail = orderWithFullProduct.user?.email;
    const emailPromises = [];

    // Customer confirmation email (with fallback to admin if missing)
    if (customerEmail) {
      emailPromises.push(
        sendOrderConfirmationEmail(orderWithFullProduct).catch(error => {
          console.error(`[EMAIL] Failed to send order confirmation to customer (${customerEmail}):`, error);
        })
      );
    } else {
      console.warn(`[EMAIL] Customer email missing for order ${orderWithFullProduct.orderNumber}. Sending confirmation to admin as fallback.`);
      emailPromises.push(
        sendOrderConfirmationEmail({
          ...orderWithFullProduct,
          user: { ...orderWithFullProduct.user, email: adminEmail, name: orderWithFullProduct.user?.name || "Customer (No Email)" },
        }).then(() => {
          console.info(`[EMAIL] Sent order confirmation fallback to admin (${adminEmail}) for order ${orderWithFullProduct.orderNumber}`);
        }).catch(error => {
          console.error(`[EMAIL] Failed to send fallback order confirmation to admin (${adminEmail}):`, error);
        })
      );
    }

    // Admin notification
    emailPromises.push(
      sendAdminOrderNotification(orderWithFullProduct, paymentMethod).catch(error => {
        console.error(`[EMAIL] Failed to send admin order notification to admin (${adminEmail}):`, error);
      })
    );

    // Delva delivery notification
    emailPromises.push(
      sendDelvaDeliveryNotification(orderWithFullProduct).catch(error => {
        console.error(`[EMAIL] Failed to send Delva delivery <NAME_EMAIL>:`, error);
      })
    );

    // Partner notification (if applicable)
    if (partner) {
      emailPromises.push(
        sendPartnerReferralNotification(orderWithFullProduct, partner).catch(error => {
          console.error(`[EMAIL] Failed to send partner referral notification to partner (${partner.email || partner.id}):`, error);
        })
      );
    }

    Promise.all(emailPromises).catch(error => {
      console.error("[EMAIL] Error sending one or more order emails:", error);
      // Don't fail the request if emails fail
    });

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: "Order created successfully",
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("Error creating order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create order" },
      { status: 500 }
    );
  }
}
