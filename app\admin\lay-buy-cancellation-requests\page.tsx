"use client";

import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import AdminCancellationRequests from "@/components/admin/lay-buy/admin-cancellation-requests";
import { useEffect, useState } from "react";
import { getCurrentUser } from "@/lib/auth-utils";
import { useRouter } from "next/navigation";
import { User } from "@/utils/types";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

export default function AdminLayBuyCancellationRequestsPage() {
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    const getUser = async () => {
      const user = await getCurrentUser()
      if (!user) {
        router.push("/sign-in")
      } else {
        setUser(user)
      }
    }
    getUser()
  }, [])

  if (loading) {
    return <div className="flex justify-center items-center h-screen"><SpinnerCircle4 /></div>
  }

  return (
    <AdminRoute>
      <AdminLayout user={user as User}>
        <AdminCancellationRequests />
      </AdminLayout>
    </AdminRoute>
  );
}
