const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function checkImages() {
  try {
    console.log('🔍 Checking product images...');
    
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        images: true
      },
      take: 10
    });

    console.log(`📊 Found ${products.length} products`);
    
    for (const product of products) {
      console.log(`\n📦 ${product.name}`);
      console.log(`   ID: ${product.id}`);
      console.log(`   Images: ${product.images.length}`);
      
      for (let i = 0; i < product.images.length; i++) {
        const imageUrl = product.images[i];
        console.log(`   ${i + 1}. ${imageUrl}`);
        
        // Test if image is accessible
        try {
          const response = await fetch(imageUrl, { method: 'HEAD' });
          console.log(`      Status: ${response.status} ${response.statusText}`);
        } catch (error) {
          console.log(`      Error: ${error.message}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkImages();
