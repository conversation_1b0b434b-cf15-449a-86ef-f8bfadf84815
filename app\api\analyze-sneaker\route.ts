import { NextResponse } from 'next/server';
import { getProductDescriptionFromImage } from '@/utils/replicate';

export async function POST(req: Request) {
  try {
    const { imageUrl } = await req.json();
    console.log('Received request...');

    if (!imageUrl) {
      console.log('No image URL provided');
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    // Convert URL to base64
    console.log('Fetching and converting image...');
    const response = await fetch(imageUrl, {
      headers: {
        'Accept': 'image/*',
        'Authorization': `Bearer ${process.env.UPLOADTHING_TOKEN}`
      }
    });

    if (!response.ok) {
      console.error('Failed to fetch image:', response.status, response.statusText);
      return NextResponse.json({
        error: 'Image access error',
        description: "We couldn't access the image. Please try again."
      }, { status: 400 });
    }

    const contentType = response.headers.get('content-type');
    if (!contentType?.startsWith('image/')) {
      return NextResponse.json({
        error: 'Invalid image',
        description: "The file is not a valid image."
      }, { status: 400 });
    }

    const arrayBuffer = await response.arrayBuffer();
    const base64Data = Buffer.from(arrayBuffer).toString('base64');
    const base64Image = `data:${contentType};base64,${base64Data}`;

    console.log('Calling Gemini API...');
    const description = await getProductDescriptionFromImage(base64Image);
    console.log('Gemini API response:', description);

    if (!description) {
      console.error('Gemini API returned null description');
      return NextResponse.json({
        error: 'AI analysis failed',
        description: "We couldn't generate a description for this image. This might happen if:\n" +
                    "1. The image is not clear enough\n" +
                    "2. The image doesn't show a complete sneaker\n" +
                    "3. The image quality is too low\n" +
                    "Please try uploading a clear, well-lit photo of the entire sneaker."
      }, { status: 400 });
    }

    return NextResponse.json({ description });

  } catch (error) {
    console.error('Error analyzing sneaker:', error);
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      
      // Return specific error messages based on the error type
      if (error.message.includes('API key')) {
        return NextResponse.json({
          error: 'API configuration error',
          description: "The Gemini API key appears to be invalid. Please check your API key configuration."
        }, { status: 500 });
      }
      
      if (error.message.includes('quota') || error.message.includes('rate limit')) {
        return NextResponse.json({
          error: 'API quota exceeded',
          description: "The Gemini API quota has been exceeded. Please try again later or contact support."
        }, { status: 429 });
      }
      
      if (error.message.includes('content type') || error.message.includes('invalid image')) {
        return NextResponse.json({
          error: 'Invalid image',
          description: "The file doesn't appear to be a valid image. Please upload a JPG, PNG, or WebP image."
        }, { status: 400 });
      }
      
      if (error.message.includes('image access') || error.message.includes('fetch failed')) {
        return NextResponse.json({
          error: 'Image access error',
          description: "We couldn't access the image. The image URL may be invalid or not publicly accessible."
        }, { status: 400 });
      }
    }
    return NextResponse.json({
      error: 'Unexpected error',
      description: "Something went wrong while analyzing the image. Please try again."
    }, { status: 500 });
  }
}
