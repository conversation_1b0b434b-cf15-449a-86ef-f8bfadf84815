"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Search,
  Users,
  Shield,
  User as UserIcon,
  Mail,
  Calendar,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Filter,
} from "lucide-react";
import { useState, useEffect } from "react";
import { UserRole } from "@/utils/types";
import { getAllUsers, updateUserRole, verifyUser } from "@/actions/userActions";

interface UserData {
  id: string;
  name: string;
  email: string;
  role: User<PERSON><PERSON>;
  emailVerified: boolean;
  isActive: boolean;
  image?: string;
  lastLoginAt?: string;
  orderCount: number;
  totalSpent: number;
  createdAt: string;
  updatedAt: string;
}

export default function AdminUsersContent() {
  const [users, setUsers] = useState<UserData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [filteredUsers, setFilteredUsers] = useState<UserData[]>([]);
  const [editingUser, setEditingUser] = useState<UserData | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    const loadUsers = async () => {
      try {
        const result = await getAllUsers();

        if (result.success && result.data) {
          // For now, we'll use mock data for order counts and spending since we don't have that in the user table
          // In a real app, you'd calculate this from the orders table
          const formattedUsers = result.data.map((user, index) => {
            let totalAmountSpent = 0;
            for (const order of user.orders) {
              totalAmountSpent += order.totalAmount;
            }

            return {
              id: user.id,
              name: user.name,
              email: user.email,
              role: user.role,
              emailVerified: user.emailVerified,
              isActive: true, // Assuming all users are active by default
              image: user.image || undefined,
              lastLoginAt: undefined, // Would need to track this in the database
              orderCount: user.orders.length || 0,
              totalSpent: totalAmountSpent || 0,
              createdAt: user.createdAt.toISOString(),
              updatedAt: user.updatedAt.toISOString(),
            };
          });
          setUsers(formattedUsers);
          setFilteredUsers(formattedUsers);
        } else {
          console.error("Error loading users:", result.error);
        }
      } catch (error) {
        console.error("Error loading users:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUsers();
  }, []);

  useEffect(() => {
    let filtered = users.filter(
      (user) =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (roleFilter !== "all") {
      filtered = filtered.filter((user) => user.role === roleFilter);
    }

    if (statusFilter !== "all") {
      if (statusFilter === "active") {
        filtered = filtered.filter((user) => user.isActive);
      } else if (statusFilter === "inactive") {
        filtered = filtered.filter((user) => !user.isActive);
      } else if (statusFilter === "verified") {
        filtered = filtered.filter((user) => user.emailVerified);
      } else if (statusFilter === "unverified") {
        filtered = filtered.filter((user) => !user.emailVerified);
      }
    }

    setFilteredUsers(filtered);
  }, [searchTerm, roleFilter, statusFilter, users]);

  const handleEditUser = (user: UserData) => {
    setEditingUser(user);
    setIsDialogOpen(true);
  };

  const handleUpdateUserRole = async (userId: string, newRole: UserRole) => {
    try {
      const result = await updateUserRole(userId, newRole);

      if (result.success) {
        const updatedUsers = users.map((user) =>
          user.id === userId
            ? { ...user, role: newRole, updatedAt: new Date().toISOString() }
            : user
        );
        setUsers(updatedUsers);
        alert(`User role updated to ${newRole} successfully!`);
      } else {
        alert(result.error || "Failed to update user role");
      }
    } catch (error) {
      console.error("Error updating user role:", error);
      alert("Failed to update user role");
    }
  };

  const handleToggleUserStatus = async (userId: string) => {
    try {
      // Mock API call - replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      const updatedUsers = users.map((user) =>
        user.id === userId
          ? {
              ...user,
              isActive: !user.isActive,
              updatedAt: new Date().toISOString(),
            }
          : user
      );
      setUsers(updatedUsers);
      alert("User status updated successfully!");
    } catch (error) {
      console.error("Error updating user status:", error);
      alert("Failed to update user status");
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this user? This action cannot be undone."
      )
    )
      return;

    try {
      // Mock API call - replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      const updatedUsers = users.filter((user) => user.id !== userId);
      setUsers(updatedUsers);
      alert("User deleted successfully!");
    } catch (error) {
      console.error("Error deleting user:", error);
      alert("Failed to delete user");
    }
  };

  const handleVerifyUser = async (userId: string) => {
    try {
      const result = await verifyUser(userId);
      if (result.success) {
        setUsers((prev) =>
          prev.map((user) =>
            user.id === userId ? { ...user, emailVerified: true } : user
          )
        );
        alert("User verified successfully!");
      } else {
        alert(result.error || "Failed to verify user");
      }
    } catch (error) {
      console.error("Error verifying user:", error);
      alert("Failed to verify user");
    }
  };

  const getRoleBadge = (role: UserRole) => {
    return role === "ADMIN" ? (
      <Badge variant="default" className="flex items-center gap-1">
        <Shield className="h-3 w-3" />
        Admin
      </Badge>
    ) : (
      <Badge variant="secondary" className="flex items-center gap-1">
        <UserIcon className="h-3 w-3" />
        User
      </Badge>
    );
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="default" className="flex items-center gap-1">
        <CheckCircle className="h-3 w-3" />
        Active
      </Badge>
    ) : (
      <Badge variant="destructive" className="flex items-center gap-1">
        <XCircle className="h-3 w-3" />
        Inactive
      </Badge>
    );
  };

  const getVerificationBadge = (emailVerified: boolean) => {
    return emailVerified ? (
      <Badge
        variant="outline"
        className="flex items-center gap-1 text-green-600"
      >
        <CheckCircle className="h-3 w-3" />
        Verified
      </Badge>
    ) : (
      <Badge
        variant="outline"
        className="flex items-center gap-1 text-orange-600"
      >
        <XCircle className="h-3 w-3" />
        Unverified
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
        </div>
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Users Management</h1>
        <p className="text-gray-600 mt-2">
          Manage user accounts, roles, and permissions
        </p>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="USER">Users</SelectItem>
                <SelectItem value="ADMIN">Admins</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="verified">Verified</SelectItem>
                <SelectItem value="unverified">Unverified</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Users</p>
                <p className="text-2xl font-bold">{users.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Admins</p>
                <p className="text-2xl font-bold">
                  {users.filter((u) => u.role === "ADMIN").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Active Users</p>
                <p className="text-2xl font-bold">
                  {users.filter((u) => u.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Verified</p>
                <p className="text-2xl font-bold">
                  {users.filter((u) => u.emailVerified).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Users List */}
      <Card>
        <CardHeader>
          <CardTitle>Users ({filteredUsers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredUsers.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No users found</p>
                {searchTerm && (
                  <p className="text-sm text-gray-500 mt-2">
                    Try adjusting your search terms or filters
                  </p>
                )}
              </div>
            ) : (
              filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center gap-4 flex-1">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={user.image} />
                      <AvatarFallback>
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-lg break-words max-w-xs md:max-w-none">{user.name}</h3>
                      <p className="text-gray-600 text-sm flex items-center gap-1 break-all">
                        <Mail className="h-4 w-4" />
                        {user.email}
                      </p>
                      <div className="flex flex-wrap items-center gap-2 mt-1">
                        {getRoleBadge(user.role)}
                        {getStatusBadge(user.isActive)}
                        {getVerificationBadge(user.emailVerified)}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4 w-full md:w-auto">
                    <div className="text-left md:text-right text-sm flex-1">
                      <p className="font-medium">{user.orderCount} orders</p>
                      <p className="text-gray-600">M{user.totalSpent.toFixed(2)} spent</p>
                      <p className="text-xs text-gray-500">
                        <Calendar className="h-3 w-3 inline mr-1" />
                        Joined {new Date(user.createdAt).toLocaleDateString()}
                      </p>
                      {user.lastLoginAt && (
                        <p className="text-xs text-gray-500">
                          Last login: {new Date(user.lastLoginAt).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                    <div className="flex flex-wrap gap-2 justify-start md:justify-end">
                      <Select
                        value={user.role}
                        onValueChange={(value) => handleUpdateUserRole(user.id, value as UserRole)}
                      >
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USER">User</SelectItem>
                          <SelectItem value="ADMIN">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleToggleUserStatus(user.id)}
                      >
                        {user.isActive ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteUser(user.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      {!user.emailVerified && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleVerifyUser(user.id)}
                        >
                          Verify
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
