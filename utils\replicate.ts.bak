import { GoogleGenerative<PERSON><PERSON> } from "@google/generative-ai";

// This ensures the API key is not exposed in client-side code
const googleApiKey = process.env.GOOGLE_AI_API_KEY;
if (!googleApiKey) {
  throw new Error("GOOGLE_AI_API_KEY environment variable is not set");
}

const genAI = new GoogleGenerativeAI(googleApiKey);

/**
 * Takes a base64 encoded image string and returns a promise that resolves with
 * the processed image data ready for Gemini API.
 */
function processBase64Image(base64Image: string): { base64: string, mimeType: string } {
  // Remove data URL prefix if present
  const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');
  
  // Try to detect MIME type from data URL, fallback to JPEG
  const mimeType = base64Image.match(/^data:([^;]+);/)?.[1] || 'image/jpeg';
  
  // Validate MIME type
  if (!mimeType.startsWith('image/')) {
    throw new Error('Invalid content type: Must be an image');
  }
  
  return { base64: base64Data, mimeType };
}

/**
 * Calls Gemini Vision API to generate a product description from a base64 encoded image.
 * This function must only be used in server-side code (API routes, getServerSideProps, etc.)
 */
export async function getProductDescriptionFromImage(base64Image: string): Promise<string | null> {
  try {
    // Ensure server-side execution
    if (typeof window !== "undefined") {
      throw new Error("This function can only be executed server-side");
    }

    console.log('Initializing Gemini model...');
    const model = genAI.getGenerativeModel({ model: "gemini-pro-vision" });
    
    // Process the base64 image
    console.log('Processing image data...');
    const { base64, mimeType } = processBase64Image(base64Image);
    console.log('Image processed. MIME type:', mimeType);
    
    console.log('Preparing content for generation...');
    const prompt = "Write a brief but detailed product description for this sneaker focusing on: " +
                  "brand name if visible, main colors, style (high/low-top), materials, unique design elements, " +
                  "and intended use (athletic/casual). Keep it to 2-3 natural, engaging sentences suitable for an " +
                  "e-commerce product page. Do not use bullet points or lists.";
    
    console.log('Calling Gemini generateContent...');
    const result = await model.generateContent([
      { text: prompt },
      {
        inlineData: {
          data: base64,
          mimeType
        }
      }
    ]);

    const response = await result.response;
    const text = response.text();
    if (!text) {
      throw new Error("Gemini API returned empty response");
    }

    // Clean up any markdown or HTML if present
    const cleanText = text
      .replace(/^[#\-*]\s+/gm, '') // Remove markdown list markers and headers
      .replace(/<[^>]+>/g, '')      // Remove HTML tags
      .replace(/\n+/g, ' ')         // Replace multiple newlines with space
      .trim();

    return cleanText || null;

  } catch (error) {
    console.error("Gemini API error:", error);
    return null;
  }
}
  } catch (error) {
    console.error("Error fetching image:", error);
    throw error;
  }
}

/**
 * Calls Gemini Vision API to generate a product description from an image URL.
 * This function must only be used in server-side code (API routes, getServerSideProps, etc.)
 * @param imageUrl The URL of the image to analyze.
 * @returns The generated product description, or null if failed.
 */
export async function getProductDescriptionFromImageGemini(imageUrl: string): Promise<string | null> {
  // Ensure server-side execution
  if (typeof window !== "undefined") {
    throw new Error("This function can only be executed server-side");
  }

  try {
    console.log('Initializing Gemini model...');
    const model = genAI.getGenerativeModel({ model: "gemini-pro-vision" });
    
    console.log('Fetching and encoding image...');
    const { base64, mimeType } = await fetchImageAsBase64(imageUrl);
    console.log('Image fetched successfully. MIME type:', mimeType);
    
    console.log('Preparing content for generation...');
    const prompt = "Write a brief but detailed product description for this sneaker focusing on: " +
                  "brand name if visible, main colors, style (high/low-top), materials, unique design elements, " +
                  "and intended use (athletic/casual). Keep it to 2-3 natural, engaging sentences suitable for an " +
                  "e-commerce product page. Do not use bullet points or lists.";
    
    console.log('Calling Gemini generateContent...');
    const result = await model.generateContent([
      { text: prompt },
      {
        inlineData: {
          data: base64,
          mimeType
        }
      }
    ]);

    console.log('Getting response from Gemini...');
    const response = await result.response;
    const text = response.text();
    
    if (!text) {
      console.error('Gemini returned empty text');
      throw new Error("Gemini API returned empty response");
    }

    console.log('Raw response from Gemini:', text.substring(0, 100) + '...');

    // Clean up any markdown or HTML if present
    const cleanText = text
      .replace(/^[#\-*]\s+/gm, '') // Remove markdown list markers and headers
      .replace(/<[^>]+>/g, '')      // Remove HTML tags
      .replace(/\n+/g, ' ')         // Replace multiple newlines with space
      .trim();

    console.log('Cleaned text:', cleanText.substring(0, 100) + '...');
    return cleanText || null;

  } catch (error) {
    console.error("Gemini API error:", error);
    // Log detailed error for debugging but return generic message for production
    return null;
  }
}

export { getProductDescriptionFromImageGemini as getProductDescriptionFromImage }; 