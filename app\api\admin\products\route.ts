import { NextRequest, NextResponse } from "next/server";
import { ApiResponse } from "@/utils/types";
import { requireAdmin } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

// GET /api/admin/products - Get all products for admin (including inactive)
export async function GET(request: NextRequest) {
  try {
    // Check admin access
    await requireAdmin();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const categoryId = searchParams.get("categoryId") || "";

    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { brand: { contains: search, mode: "insensitive" } },
      ];
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    const skip = (page - 1) * limit;
    const total = await prisma.product.count({ where });

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: { 
            reviews: true,
            orderItems: true,
            cartItems: true
          }
        }
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    });

    const response: ApiResponse = {
      success: true,
      data: {
        data: products,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching admin products:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}

// POST /api/admin/products - Create new product
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    await requireAdmin();

    const body = await request.json();
    const {
      name,
      description,
      price,
      discountedPrice,
      brand,
      categoryId,
      images,
      sizes,
      colors,
      stock,
      costPrice,
      shippingFee,
      lateCollectionFee,
    } = body;

    // Validate required fields
    if (!name || !price || !brand || !categoryId) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Verify category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { success: false, error: "Category not found" },
        { status: 400 }
      );
    }

    const product = await prisma.product.create({
      data: {
        name,
        description,
        price: parseFloat(price),
        discountedPrice: discountedPrice ? parseFloat(discountedPrice) : null,
        brand,
        categoryId,
        images: images || [],
        sizes: sizes || [],
        colors: colors || [],
        stock: parseInt(stock) || 0,
        costPrice: costPrice !== undefined ? parseFloat(costPrice) : null,
        shippingFee: shippingFee !== undefined ? parseFloat(shippingFee) : 100,
        lateCollectionFee: lateCollectionFee !== undefined ? parseFloat(lateCollectionFee) : 0,
      },
      include: {
        category: true,
      },
    });

    const response: ApiResponse<typeof product> = {
      success: true,
      data: product,
      message: "Product created successfully",
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("Error creating product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create product" },
      { status: 500 }
    );
  }
}


