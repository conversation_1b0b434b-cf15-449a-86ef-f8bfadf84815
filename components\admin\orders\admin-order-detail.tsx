"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  ArrowLeft,
  User,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Package,
  DollarSign,
  Clock,
  CheckCircle,
  Truck,
  XCircle,
  Edit,
  Save,
  Image as ImageIcon,
  Eye,
  AlertCircle
} from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import { OrderStatus } from "@/utils/types";
import { getOrderById, updateOrder } from "@/actions/orderActions";

interface OrderDetailData {
  id: string;
  orderNumber: string;
  status: OrderStatus;
  totalAmount: number;
  discountAmount: number;
  shippingAddress: string;
  phoneNumber: string;
  notes?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    productName: string;
    productBrand: string;
    productImages: string[];
    quantity: number;
    price: number;
    size: string | null;
    color: string | null;
  }>;
  paymentProof?: {
    id: string;
    imageUrl: string;
    status: string;
    notes?: string;
    verifiedBy?: string;
    verifiedAt?: string;
    createdAt: string;
  } | null;
}

interface AdminOrderDetailProps {
  orderId: string;
}

export default function AdminOrderDetail({ orderId }: AdminOrderDetailProps) {
  const [order, setOrder] = useState<OrderDetailData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [newStatus, setNewStatus] = useState<OrderStatus>("PENDING");
  const [adminNotes, setAdminNotes] = useState("");
  const [isUpdatingPayment, setIsUpdatingPayment] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState("");
  const [trackingUrl, setTrackingUrl] = useState("");

  useEffect(() => {
    const loadOrderDetail = async () => {
      try {
        const result = await getOrderById(orderId);

        if (result.success && result.data) {
          const orderData = result.data;
          const formattedOrder: OrderDetailData = {
            id: orderData.id,
            orderNumber: orderData.orderNumber,
            status: orderData.status,
            totalAmount: orderData.totalAmount,
            discountAmount: orderData.discountAmount || 0,
            shippingAddress: orderData.shippingAddress,
            phoneNumber: orderData.phoneNumber,
            notes: orderData.notes || "",
            adminNotes: orderData.adminNotes || "",
            createdAt: orderData.createdAt.toISOString(),
            updatedAt: orderData.updatedAt.toISOString(),
            customer: {
              id: orderData.user.id,
              name: orderData.user.name,
              email: orderData.user.email
            },
            items: orderData.orderItems.map(item => ({
              id: item.id,
              productName: item.product.name,
              productBrand: item.product.brand,
              productImages: item.product.images,
              quantity: item.quantity,
              price: item.price,
              size: item.size,
              color: item.color
            })),
            paymentProof: orderData.paymentProof ? {
              id: orderData.paymentProof.id,
              imageUrl: orderData.paymentProof.imageUrl,
              status: orderData.paymentProof.status,
              notes: orderData.paymentProof.notes || undefined,
              verifiedBy: orderData.paymentProof.verifiedBy || undefined,
              verifiedAt: orderData.paymentProof.verifiedAt?.toISOString(),
              createdAt: orderData.paymentProof.createdAt.toISOString()
            } : null
          };

          setOrder(formattedOrder);
          setNewStatus(formattedOrder.status);
          setAdminNotes(formattedOrder.adminNotes || "");
        } else {
          console.error("Error loading order detail:", result.error);
        }
      } catch (error) {
        console.error("Error loading order detail:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadOrderDetail();
  }, [orderId]);

  const handleStatusUpdate = async () => {
    if (!order) return;

    try {
      setIsUpdatingStatus(true);
      const response = await fetch(`/api/admin/orders/${order.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: newStatus,
          trackingNumber: trackingNumber || undefined,
          trackingUrl: trackingUrl || undefined,
          adminNotes: adminNotes || undefined,
        }),
      });

      if (!response.ok) throw new Error('Failed to update status');

      const result = await response.json();
      if (result.success) {
        // Reload the order data
        const orderResult = await getOrderById(orderId);
        if (orderResult.success && orderResult.data) {
          const orderData = orderResult.data;
          const formattedOrder: OrderDetailData = {
            id: orderData.id,
            orderNumber: orderData.orderNumber,
            status: orderData.status,
            totalAmount: orderData.totalAmount,
            discountAmount: orderData.discountAmount || 0,
            shippingAddress: orderData.shippingAddress,
            phoneNumber: orderData.phoneNumber,
            notes: orderData.notes || "",
            adminNotes: orderData.adminNotes || "",
            createdAt: orderData.createdAt.toISOString(),
            updatedAt: orderData.updatedAt.toISOString(),
            customer: {
              id: orderData.user.id,
              name: orderData.user.name,
              email: orderData.user.email
            },
            items: orderData.orderItems.map(item => ({
              id: item.id,
              productName: item.product.name,
              productBrand: item.product.brand,
              productImages: item.product.images,
              quantity: item.quantity,
              price: item.price,
              size: item.size,
              color: item.color
            })),
            paymentProof: orderData.paymentProof ? {
              id: orderData.paymentProof.id,
              imageUrl: orderData.paymentProof.imageUrl,
              status: orderData.paymentProof.status,
              notes: orderData.paymentProof.notes || undefined,
              createdAt: orderData.paymentProof.createdAt.toISOString(),
            } : undefined
          };
          setOrder(formattedOrder);
          setAdminNotes(formattedOrder.adminNotes || "");
        }
        alert('Order status updated successfully!');
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      alert("Failed to update order status");
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handlePaymentProofUpdate = async (status: string, notes?: string) => {
    if (!order?.paymentProof) return;

    setIsUpdatingPayment(true);
    try {
      const response = await fetch(`/api/admin/payment-proof/${order.paymentProof.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, notes }),
      });

      const result = await response.json();

      if (result.success) {
        // Reload the order to get updated data
        const orderResult = await getOrderById(order.id);
        if (orderResult.success && orderResult.data) {
          const orderData = orderResult.data;
          const formattedOrder: OrderDetailData = {
            id: orderData.id,
            orderNumber: orderData.orderNumber,
            status: orderData.status,
            totalAmount: orderData.totalAmount,
            discountAmount: orderData.discountAmount || 0,
            shippingAddress: orderData.shippingAddress,
            phoneNumber: orderData.phoneNumber,
            notes: orderData.notes || "",
            adminNotes: orderData.adminNotes || "",
            createdAt: orderData.createdAt.toISOString(),
            updatedAt: orderData.updatedAt.toISOString(),
            customer: {
              id: orderData.user.id,
              name: orderData.user.name,
              email: orderData.user.email
            },
            items: orderData.orderItems.map(item => ({
              id: item.id,
              productName: item.product.name,
              productBrand: item.product.brand,
              productImages: item.product.images,
              quantity: item.quantity,
              price: item.price,
              size: item.size,
              color: item.color
            })),
            paymentProof: orderData.paymentProof ? {
              id: orderData.paymentProof.id,
              imageUrl: orderData.paymentProof.imageUrl,
              status: orderData.paymentProof.status,
              notes: orderData.paymentProof.notes || undefined,
              verifiedBy: orderData.paymentProof.verifiedBy || undefined,
              verifiedAt: orderData.paymentProof.verifiedAt?.toISOString(),
              createdAt: orderData.paymentProof.createdAt.toISOString()
            } : null
          };
          setOrder(formattedOrder);
          setNewStatus(formattedOrder.status);
        }
        alert("Payment proof updated successfully!");
      } else {
        alert(result.error || "Failed to update payment proof");
      }
    } catch (error) {
      console.error("Error updating payment proof:", error);
      alert("Failed to update payment proof");
    } finally {
      setIsUpdatingPayment(false);
    }
  };

  const getStatusBadge = (status: OrderStatus) => {
    const statusConfig = {
      PENDING: { variant: "secondary" as const, icon: Clock, color: "text-yellow-600" },
      PAID: { variant: "default" as const, icon: CheckCircle, color: "text-green-600" },
      CONFIRMED: { variant: "default" as const, icon: CheckCircle, color: "text-blue-600" },
      PROCESSING: { variant: "outline" as const, icon: Package, color: "text-purple-600" },
      SHIPPED: { variant: "outline" as const, icon: Truck, color: "text-orange-600" },
      DELIVERED: { variant: "default" as const, icon: CheckCircle, color: "text-green-600" },
      CANCELLED: { variant: "destructive" as const, icon: XCircle, color: "text-red-600" }
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="text-center py-12">
        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">Order not found</p>
        <Link href="/admin/orders">
          <Button variant="outline" className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
        </Link>
      </div>
    );
  }

  const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/orders">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Orders
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{order.orderNumber}</h1>
            <div className="flex items-center gap-2 mt-1">
              {getStatusBadge(order.status)}
              <span className="text-sm text-gray-500">
                <Calendar className="h-4 w-4 inline mr-1" />
                {new Date(order.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Name</p>
                  <p className="font-medium">{order.customer.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <p className="font-medium flex items-center gap-1">
                    <Mail className="h-4 w-4" />
                    {order.customer.email}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Phone</p>
                  <p className="font-medium flex items-center gap-1">
                    <Phone className="h-4 w-4" />
                    {order.phoneNumber}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Shipping Address</p>
                  <p className="font-medium flex items-start gap-1">
                    <MapPin className="h-4 w-4 mt-0.5" />
                    {order.shippingAddress}
                  </p>
                </div>
              </div>
              {order.notes && (
                <div>
                  <p className="text-sm text-blue-700 font-semibold">Lay Buy Details</p>
                  <p className="font-medium bg-blue-50 p-3 rounded-lg">{order.notes}</p>
                </div>
              )}
              {order.notes && (
                <div>
                  <p className="text-sm text-gray-600">Customer Notes</p>
                  <p className="font-medium bg-gray-50 p-3 rounded-lg">{order.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Order Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                        {item.productImages && item.productImages.length > 0 ? (
                          <img
                            src={item.productImages[0]}
                            alt={item.productName}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        <Package className="h-6 w-6 text-gray-400 hidden" />
                      </div>
                      <div>
                        <h4 className="font-semibold">{item.productName}</h4>
                        <p className="text-sm text-gray-600">{item.productBrand}</p>
                        <p className="text-sm text-gray-600">
                          Size: {item.size || 'N/A'} • Color: {item.color || 'N/A'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">M{item.price.toFixed(2)}</p>
                      <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                      <p className="text-sm font-medium">
                        Total: M{(item.price * item.quantity).toFixed(2)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Proof */}
          {order.paymentProof && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ImageIcon className="h-5 w-5" />
                  Payment Proof
                  <Badge
                    variant={
                      order.paymentProof.status === 'VERIFIED' ? 'default' :
                      order.paymentProof.status === 'REJECTED' ? 'destructive' :
                      'secondary'
                    }
                  >
                    {order.paymentProof.status}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <img
                    src={order.paymentProof.imageUrl}
                    alt="Payment proof"
                    className="w-full max-w-md rounded-lg border shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => window.open(order.paymentProof!.imageUrl, '_blank')}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={() => window.open(order.paymentProof!.imageUrl, '_blank')}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Full Size
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Uploaded:</p>
                    <p className="font-medium">
                      {new Date(order.paymentProof.createdAt).toLocaleString()}
                    </p>
                  </div>
                  {order.paymentProof.verifiedAt && (
                    <div>
                      <p className="text-gray-600">Verified:</p>
                      <p className="font-medium">
                        {new Date(order.paymentProof.verifiedAt).toLocaleString()}
                      </p>
                    </div>
                  )}
                  {order.paymentProof.verifiedBy && (
                    <div>
                      <p className="text-gray-600">Verified by:</p>
                      <p className="font-medium">{order.paymentProof.verifiedBy}</p>
                    </div>
                  )}
                  {order.paymentProof.notes && (
                    <div className="md:col-span-2">
                      <p className="text-gray-600">Admin Notes:</p>
                      <p className="font-medium bg-gray-50 p-2 rounded">{order.paymentProof.notes}</p>
                    </div>
                  )}
                </div>

                {/* Payment Verification Actions */}
                {order.paymentProof.status === 'PENDING' && (
                  <div className="flex gap-2 pt-4 border-t">
                    <Button
                      onClick={() => handlePaymentProofUpdate('VERIFIED')}
                      disabled={isUpdatingPayment}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Verify Payment
                    </Button>
                    <Button
                      onClick={() => {
                        const reason = prompt("Enter rejection reason:");
                        if (reason) {
                          handlePaymentProofUpdate('REJECTED', reason);
                        }
                      }}
                      disabled={isUpdatingPayment}
                      variant="destructive"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject Payment
                    </Button>
                  </div>
                )}

                {order.paymentProof.status !== 'PENDING' && (
                  <div className="flex gap-2 pt-4 border-t">
                    <Button
                      onClick={() => handlePaymentProofUpdate('PENDING')}
                      disabled={isUpdatingPayment}
                      variant="outline"
                    >
                      <Clock className="h-4 w-4 mr-2" />
                      Reset to Pending
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {!order.paymentProof && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600" />
                  Payment Proof
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-6">
                  <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">No payment proof uploaded yet</p>
                  <p className="text-sm text-gray-500 mt-1">
                    Customer needs to upload payment proof for verification
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Order Summary & Actions */}
        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Order Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>M{subtotal.toFixed(2)}</span>
              </div>
              {order.discountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount:</span>
                  <span>-M{order.discountAmount.toFixed(2)}</span>
                </div>
              )}
              <div className="border-t pt-3">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total:</span>
                  <span>M{order.totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                Update Order
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Order Status</label>
                <Select value={newStatus} onValueChange={(value) => setNewStatus(value as OrderStatus)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                    <SelectItem value="PROCESSING">Processing</SelectItem>
                    <SelectItem value="SHIPPED">Shipped</SelectItem>
                    <SelectItem value="DELIVERED">Delivered</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium">Admin Notes</label>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add internal notes about this order..."
                  className="mt-1"
                  rows={3}
                />
              </div>
              
              <Button 
                onClick={handleStatusUpdate}
                disabled={isUpdating}
                className="w-full"
              >
                <Save className="h-4 w-4 mr-2" />
                {isUpdating ? "Updating..." : "Update Order"}
              </Button>
            </CardContent>
          </Card>

          {/* Order Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Order Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium">Order Created</p>
                    <p className="text-xs text-gray-500">
                      {new Date(order.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>
                {order.updatedAt !== order.createdAt && (
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Last Updated</p>
                      <p className="text-xs text-gray-500">
                        {new Date(order.updatedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
