import type { Metadata } from "next";
import { DM_Sans } from "next/font/google";
import "./globals.css";
import { CartProvider } from "@/contexts/cart-context";
import PrimaryColorClient from "@/components/ui/primary-color-client";
import { NextSSRPlugin } from "@uploadthing/react/next-ssr-plugin";
import { extractRouterConfig } from "uploadthing/server";

import { ourFileRouter } from "./api/uploadthing/core";

import { Analytics } from "@vercel/analytics/next";
import { SpeedInsights } from "@vercel/speed-insights/next";
import ReferralCodeListener from '@/components/customized/referral-code-listener';
import ScrollToTop from '@/components/ui/scroll-to-top';
import NotificationPopup from '@/components/notifications/notification-popup';

const font = DM_Sans({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Home | Rivv",
  description:
    "Purposefully Curated. Unapologetically Premium. RIVV is a proudly female-founded brand on a mission to deliver quality that speaks for itself. Every pair is carefully selected for its craftsmanship, comfort, and standout style. We're not here to meet expectations—we're here to exceed them. Step in with confidence. You won't be disappointed.",
  keywords:
    "premium sneakers, footwear Lesotho, luxury shoes, athletic sneakers, fashion footwear, Maseru shoes, quality sneakers, comfort shoes, stylish sneakers, female-founded brand",
  openGraph: {
    title: "Home | Rivv",
    description:
      "Purposefully Curated. Unapologetically Premium. RIVV Premium Sneakers - Quality footwear in Lesotho.",
    type: "website",
    locale: "en_LS",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // No useEffect here; use client component instead

  return (
    <html lang="en">
      <body className={font.className}>
        <ReferralCodeListener />
        <NextSSRPlugin
          /**
           * The `extractRouterConfig` will extract **only** the route configs
           * from the router to prevent additional information from being
           * leaked to the client. The data passed to the client is the same
           * as if you were to fetch `/api/uploadthing` directly.
           */
          routerConfig={extractRouterConfig(ourFileRouter)}
        />
        <PrimaryColorClient />
        <CartProvider>
          {children}
          <ScrollToTop />
          <NotificationPopup />
        </CartProvider>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
