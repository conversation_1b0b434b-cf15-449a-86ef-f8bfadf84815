import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";
import { getFeeSettings, calculateDeliveryFee } from "@/lib/fee-utils";
import { sendLayBuyOrderConfirmation } from "@/lib/email-service";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// POST /api/lay-buy-orders - Create new Lay-Buy order
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      shippingAddress,
      phoneNumber,
      notes,
      discountAmount = 0,
      discountCode,
      paymentMethod,
      paymentProofUrl,
      deliveryFee: deliveryFeeParam = 0,
      items,
      layBuy,
    } = body;

    // Validate required fields
    if (!shippingAddress || !phoneNumber || !items || items.length === 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    if (!layBuy || !layBuy.totalAmount || !layBuy.upfrontAmount || !layBuy.remainingAmount) {
      return NextResponse.json(
        { success: false, error: "Missing Lay-Buy information" },
        { status: 400 }
      );
    }

    // Validate items exist and have sufficient stock
    const productIds = items.map((item: any) => item.productId);
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } },
    });

    if (products.length !== productIds.length) {
      return NextResponse.json(
        { success: false, error: "Some products not found" },
        { status: 400 }
      );
    }

    // Check stock availability
    for (const item of items) {
      const product = products.find(p => p.id === item.productId);
      if (!product || product.stock < item.quantity) {
        return NextResponse.json(
          { success: false, error: `Insufficient stock for ${product?.name || 'product'}` },
          { status: 400 }
        );
      }
    }

    // Calculate total shoes for delivery fee
    let totalShoes = 0;
    const orderItemsData = items.map((item: any) => {
      totalShoes += item.quantity;
      return {
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        size: item.size || "",
        color: item.color || "",
      };
    });

    // Get fee settings and calculate delivery fee
    const feeSettings = await getFeeSettings();
    const deliveryCalculation = calculateDeliveryFee(totalShoes, feeSettings);
    const deliveryFee = deliveryCalculation.fee;
    const isBulkDelivery = deliveryCalculation.isBulk;

    // Create Lay-Buy order in transaction with increased timeout
    const result = await prisma.$transaction(async (tx) => {
      // Update product stock
      const stockUpdates = items.map((item: any) =>
        tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        })
      );
      await Promise.all(stockUpdates);

      // Clear user's cart
      await tx.cartItem.deleteMany({
        where: { userId: user.id },
      });

      // Create the Lay-Buy order directly in the transaction
      const orderNumber = `LB-${Date.now().toString(36).toUpperCase()}-${Math.random().toString(36).substring(2, 6).toUpperCase()}`;
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + (6 * 7)); // 6 weeks
      const gracePeriodEnd = new Date(dueDate);
      gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 7); // 1 week grace period

      const layBuyOrder = await tx.layBuyOrder.create({
        data: {
          userId: user.id,
          orderNumber,
          status: "ACTIVE",
          totalAmount: layBuy.totalAmount,
          upfrontAmount: layBuy.upfrontAmount,
          remainingAmount: layBuy.remainingAmount,
          amountPaid: layBuy.upfrontAmount,
          dueDate,
          gracePeriodEnd,
          shippingAddress,
          phoneNumber,
          notes,
          orderItems: {
            create: orderItemsData,
          },
        },
        include: {
          orderItems: {
            include: {
              product: true,
            },
          },
        },
      });

      // Add the upfront payment record if payment proof is provided
      if (paymentProofUrl) {
        await tx.layBuyPayment.create({
          data: {
            layBuyOrderId: layBuyOrder.id,
            amount: layBuy.upfrontAmount,
            paymentType: "UPFRONT",
            paymentMethod,
            paymentProof: paymentProofUrl,
            notes: `Upfront payment for Lay-Buy order ${layBuyOrder.orderNumber}`,
            status: "PENDING", // Will be verified by admin
          },
        });
      }

      return layBuyOrder;
    }, {
      timeout: 15000, // Increase timeout to 15 seconds
    });

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
    };

    // Send confirmation email asynchronously (don't block the response)
    try {
      // Refetch the order with user data for email
      const orderWithUser = await prisma.layBuyOrder.findUnique({
        where: { id: result.id },
        include: {
          user: true,
          orderItems: {
            include: {
              product: true,
            },
          },
        },
      });

      if (orderWithUser) {
        await sendLayBuyOrderConfirmation(orderWithUser);
      }
    } catch (emailError) {
      console.error("Failed to send Lay-Buy order confirmation email:", emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error creating Lay-Buy order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create Lay-Buy order" },
      { status: 500 }
    );
  }
}

// GET /api/lay-buy-orders - Get user's Lay-Buy orders
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const orders = await prisma.layBuyOrder.findMany({
      where: { userId: user.id },
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        payments: {
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: {
            payments: true,
            reminders: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    const response: ApiResponse<typeof orders> = {
      success: true,
      data: orders,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching Lay-Buy orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch Lay-Buy orders" },
      { status: 500 }
    );
  }
}
