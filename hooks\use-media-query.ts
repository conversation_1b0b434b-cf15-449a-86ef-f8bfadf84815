import { useState, useEffect } from 'react';

type MediaQuery = `(max-width: ${number}px)` | `(min-width: ${number}px)` | string;

/**
 * A custom React hook that tracks whether a given media query matches.
 * @param query - The media query string to match against (e.g., '(max-width: 768px)')
 * @returns boolean - Whether the media query matches the current viewport
 */
export function useMediaQuery(query: MediaQuery): boolean {
  const [matches, setMatches] = useState<boolean>(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    
    // Set the initial value
    setMatches(mediaQuery.matches);
    
    // Create a callback function to handle changes
    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };
    
    // Add the listener for changes
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', listener);
    } else {
      // For older browsers
      mediaQuery.addListener(listener);
    }
    
    // Clean up the listener when the component unmounts
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', listener);
      } else {
        mediaQuery.removeListener(listener);
      }
    };
  }, [query]);

  return matches;
}

/**
 * Predefined media query hooks for common breakpoints
 */

export function useIsMobile() {
  return useMediaQuery('(max-width: 640px)');
}

export function useIsTablet() {
  return useMediaQuery('(min-width: 641px) and (max-width: 1024px)');
}

export function useIsDesktop() {
  return useMediaQuery('(min-width: 1025px)');
}
