#!/usr/bin/env node

/**
 * Systematic Product Price Update Script
 *
 * This script implements systematic price increases across all products:
 * - Products between M1000 and M1400: increase by M250
 * - All other products (below M1000 or above M1400): increase by M300
 *
 * Usage: node scripts/update-product-prices.ts [--dry-run] [--batch-size=50]
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface PriceUpdateStats {
  total: number;
  processed: number;
  updated: number;
  skipped: number;
  errors: number;
}

interface UpdateOptions {
  dryRun: boolean;
  batchSize: number;
}

// Parse command line arguments
function parseArgs(): UpdateOptions {
  const args = process.argv.slice(2);
  const options: UpdateOptions = {
    dryRun: false,
    batchSize: 50,
  };

  for (const arg of args) {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--batch-size=')) {
      options.batchSize = parseInt(arg.split('=')[1]) || 50;
    } else if (arg === '--help') {
      console.log(`
Usage: node scripts/update-product-prices.ts [options]

Options:
  --dry-run         Show what would be updated without making changes
  --batch-size=N    Process N products at a time (default: 50)
  --help            Show this help message

Price Update Rules:
  - Products between M1000 and M1400: increase by M250
  - All other products: increase by M300
      `);
      process.exit(0);
    }
  }

  return options;
}

// Calculate new price based on current price
function calculateNewPrice(currentPrice: number): { newPrice: number; increase: number } {
  let increase: number;
  
  if (currentPrice >= 1000 && currentPrice <= 1400) {
    increase = 250;
  } else {
    increase = 300;
  }
  
  const newPrice = currentPrice + increase;
  return { newPrice, increase };
}

// Update a single product's price
async function updateProductPrice(
  product: { id: string; name: string; price: number; discountedPrice?: number | null },
  options: UpdateOptions,
  stats: PriceUpdateStats
): Promise<boolean> {
  try {
    const { newPrice, increase } = calculateNewPrice(product.price);
    
    // Calculate new discounted price if it exists
    let newDiscountedPrice: number | undefined;
    if (product.discountedPrice) {
      const discountAmount = product.price - product.discountedPrice;
      newDiscountedPrice = newPrice - discountAmount;
      // Ensure discounted price doesn't go below 0
      if (newDiscountedPrice < 0) {
        newDiscountedPrice = newPrice * 0.9; // 10% discount as fallback
      }
    }

    console.log(`📦 ${product.name}`);
    console.log(`   Current: M${product.price.toFixed(2)} → New: M${newPrice.toFixed(2)} (+M${increase})`);
    if (product.discountedPrice && newDiscountedPrice) {
      console.log(`   Discounted: M${product.discountedPrice.toFixed(2)} → M${newDiscountedPrice.toFixed(2)}`);
    }

    if (!options.dryRun) {
      const updateData: any = {
        price: newPrice,
        updatedAt: new Date(),
      };

      if (newDiscountedPrice) {
        updateData.discountedPrice = newDiscountedPrice;
      }

      await prisma.product.update({
        where: { id: product.id },
        data: updateData,
      });
    }

    stats.updated++;
    return true;
  } catch (error) {
    console.error(`❌ Error updating ${product.name}:`, error);
    stats.errors++;
    return false;
  }
}

// Process products in batches
async function processBatch(
  products: Array<{ id: string; name: string; price: number; discountedPrice?: number | null }>,
  batchIndex: number,
  options: UpdateOptions,
  stats: PriceUpdateStats
): Promise<void> {
  console.log(`\n🔄 Processing batch ${batchIndex + 1} (${products.length} products):`);
  
  for (const product of products) {
    await updateProductPrice(product, options, stats);
    stats.processed++;
  }
}

// Get all active products
async function getProductsToUpdate(): Promise<Array<{ id: string; name: string; price: number; discountedPrice?: number | null }>> {
  return await prisma.product.findMany({
    where: {
      isActive: true,
    },
    select: {
      id: true,
      name: true,
      price: true,
      discountedPrice: true,
    },
    orderBy: {
      price: 'asc',
    },
  });
}

// Main execution function
async function main() {
  const options = parseArgs();
  const stats: PriceUpdateStats = {
    total: 0,
    processed: 0,
    updated: 0,
    skipped: 0,
    errors: 0,
  };

  console.log('🚀 Starting systematic price update...');
  console.log(`📋 Mode: ${options.dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE UPDATE'}`);
  console.log(`📦 Batch size: ${options.batchSize}`);
  console.log('\n📊 Price Update Rules:');
  console.log('   • Products M1000-M1400: +M250');
  console.log('   • All other products: +M300\n');

  try {
    // Get all products to update
    const allProducts = await getProductsToUpdate();
    stats.total = allProducts.length;
    
    if (stats.total === 0) {
      console.log('✨ No active products found to update.');
      return;
    }
    
    console.log(`📋 Found ${stats.total} active products to update\n`);
    
    // Process in batches
    for (let i = 0; i < allProducts.length; i += options.batchSize) {
      const batch = allProducts.slice(i, i + options.batchSize);
      const batchIndex = Math.floor(i / options.batchSize);
      
      await processBatch(batch, batchIndex, options, stats);
      
      // Progress update
      const progress = ((stats.processed / stats.total) * 100).toFixed(1);
      console.log(`📊 Progress: ${stats.processed}/${stats.total} (${progress}%)`);
      
      // Small delay between batches
      if (i + options.batchSize < allProducts.length) {
        console.log('⏳ Waiting 1 second before next batch...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // Final summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 PRICE UPDATE SUMMARY');
    console.log('='.repeat(50));
    console.log(`📦 Total products: ${stats.total}`);
    console.log(`✅ Successfully updated: ${stats.updated}`);
    console.log(`⏭️  Skipped: ${stats.skipped}`);
    console.log(`❌ Errors: ${stats.errors}`);
    
    if (options.dryRun) {
      console.log('\n🔍 This was a dry run - no changes were made.');
      console.log('💡 Run without --dry-run to apply the price updates.');
    } else {
      console.log('\n✅ Price update completed successfully!');
    }
    
  } catch (error) {
    console.error('💥 Fatal error during price update:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Process interrupted. Cleaning up...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n⚠️  Process terminated. Cleaning up...');
  await prisma.$disconnect();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}
