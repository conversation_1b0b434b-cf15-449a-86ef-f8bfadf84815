import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UploadDropzone } from "@/lib/uploadthing";
import { X, Upload, Package } from "lucide-react";
import { Switch } from "@/components/ui/switch";

export interface Category {
  id: string;
  name: string;
  description?: string;
}

interface ProductFormProps {
  initialProduct?: any;
  categories: Category[];
  onSubmit: (data: any) => void;
  loading: boolean;
  error: string;
  mode: "create" | "edit";
}

const defaultSizes = ["3", "4", "5", "6", "7", "8", "9", "10", "11", "12"];

export default function ProductForm({
  initialProduct,
  categories,
  onSubmit,
  loading,
  error,
  mode,
}: ProductFormProps) {
  const [form, setForm] = useState({
    name: initialProduct?.name || "",
    description: initialProduct?.description || "",
    price: initialProduct?.price?.toString() || "",
    discountedPrice: initialProduct?.discountedPrice?.toString() || "",
    brand: initialProduct?.brand || "",
    categoryId: initialProduct?.categoryId || "",
    stock: initialProduct?.stock?.toString() || "",
  });
  const [selectedSizes, setSelectedSizes] = useState<string[]>(initialProduct?.sizes || []);
  const [images, setImages] = useState<string[]>(initialProduct?.images || []);
  const [lastUploadResponse, setLastUploadResponse] = useState<any>(null);
  const [feeForm, setFeeForm] = useState({
    costPrice: initialProduct?.costPrice?.toString() || "",
    shippingFee: initialProduct?.shippingFee?.toString() || "",
    lateCollectionFee: initialProduct?.lateCollectionFee ? true : false,
  });
  const [defaultFees, setDefaultFees] = useState({
    costPrice: "",
    shippingFee: "",
    lateCollectionFee: false,
  });

  useEffect(() => {
    if (initialProduct) {
      setForm({
        name: initialProduct.name || "",
        description: initialProduct.description || "",
        price: initialProduct.price?.toString() || "",
        discountedPrice: initialProduct.discountedPrice?.toString() || "",
        brand: initialProduct.brand || "",
        categoryId: initialProduct.categoryId || "",
        stock: initialProduct.stock?.toString() || "",
      });
      setSelectedSizes(initialProduct.sizes || []);
      setImages(initialProduct.images || []);
    }
  }, [initialProduct]);

  useEffect(() => {
    if (!initialProduct) {
      // Fetch default fees from settings API
      fetch("/api/admin/settings").then(res => res.json()).then(result => {
        if (result.success && result.data) {
          setFeeForm({
            costPrice: "",
            shippingFee: result.data.defaultShippingFee?.toString() || "100",
            lateCollectionFee: false,
          });
          setDefaultFees({
            costPrice: "",
            shippingFee: result.data.defaultShippingFee?.toString() || "100",
            lateCollectionFee: false,
          });
        }
      });
    }
  }, [initialProduct]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleImageUpload = (uploadedFiles: any[]) => {
    if (!uploadedFiles || !Array.isArray(uploadedFiles)) {
      console.error("Invalid uploaded files:", uploadedFiles);
      return;
    }
    const newImageUrls = uploadedFiles
      .filter(file => file && file.url)
      .map(file => file.url);
    if (newImageUrls.length > 0) {
      setImages(prev => [...prev, ...newImageUrls]);
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleFeeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFeeForm({ ...feeForm, [e.target.name]: e.target.value });
  };
  const handleLateFeeToggle = (checked: boolean) => {
    setFeeForm({ ...feeForm, lateCollectionFee: checked });
  };

  const handleAiGenerate = async () => {
    if (images.length === 0) {
      alert("Please upload an image first to generate AI description.");
      return;
    }
    try {
      const res = await fetch("/api/admin/analyze-product", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageUrl: images[0] }),
      });
      const result = await res.json();
      if (result.description) {
        const aiData = JSON.parse(result.description);
        setForm(prev => ({
          ...prev,
          name: aiData.name || prev.name,
          description: aiData.description || prev.description,
          brand: aiData.brand || prev.brand,
        }));
      } else {
        alert("AI could not generate a description. Please try another image.");
      }
    } catch (error) {
      alert("Failed to generate AI description.");
    }
  };

  const validateForm = () => {
    if (!form.name.trim()) return "Product name is required";
    if (!form.price || parseFloat(form.price) <= 0) return "Valid price is required";
    if (!form.brand.trim()) return "Brand is required";
    if (!form.categoryId) return "Category is required";
    if (selectedSizes.length === 0) return "At least one size must be selected";
    if (images.length === 0) return "At least one image is required";
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }
    onSubmit({
      ...form,
      price: parseFloat(form.price),
      discountedPrice: form.discountedPrice ? parseFloat(form.discountedPrice) : undefined,
      images,
      sizes: selectedSizes,
      stock: form.stock ? parseInt(form.stock) : 0,
      costPrice: feeForm.costPrice ? parseFloat(feeForm.costPrice) : undefined,
      shippingFee: feeForm.shippingFee ? parseFloat(feeForm.shippingFee) : undefined,
      lateCollectionFee: feeForm.lateCollectionFee ? 10 : 0, // Use 10 as default, or fetch from settings if needed
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {mode === "edit" ? "Edit Product" : "Basic Information"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Product Name *</Label>
              <Input 
                id="name" 
                name="name" 
                value={form.name} 
                onChange={handleChange} 
                placeholder="Enter product name"
                required 
              />
            </div>
            <div>
              <Label htmlFor="brand">Brand *</Label>
              <Input 
                id="brand" 
                name="brand" 
                value={form.brand} 
                onChange={handleChange} 
                placeholder="Enter brand name"
                required 
              />
            </div>
          </div>
          <div>
            <div className="flex justify-between items-center">
              <Label htmlFor="description">Description</Label>
              <Button type="button" variant="outline" size="sm" onClick={handleAiGenerate}>
                ✨ Generate AI Description
              </Button>
            </div>
            <Textarea 
              id="description" 
              name="description" 
              value={form.description} 
              onChange={handleChange} 
              placeholder="Enter product description"
              rows={4}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="price">Price (M) *</Label>
              <Input 
                id="price" 
                name="price" 
                type="number" 
                step="0.01" 
                min="0"
                value={form.price} 
                onChange={handleChange} 
                placeholder="0.00"
                required 
              />
            </div>
            <div>
              <Label htmlFor="discountedPrice">Discounted Price (M)</Label>
              <Input 
                id="discountedPrice" 
                name="discountedPrice" 
                type="number" 
                step="0.01" 
                min="0"
                value={form.discountedPrice} 
                onChange={handleChange} 
                placeholder="0.00"
              />
            </div>
            <div>
              <Label htmlFor="stock">Stock Quantity</Label>
              <Input 
                id="stock" 
                name="stock" 
                type="number" 
                min="0"
                value={form.stock} 
                onChange={handleChange} 
                placeholder="0"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="categoryId">Category *</Label>
            <Select 
              value={form.categoryId} 
              onValueChange={(value) => setForm(prev => ({ ...prev, categoryId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
      {/* Sizes only, remove Colors section */}
      <Card>
        <CardHeader>
          <CardTitle>Sizes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label>Available Sizes *</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {defaultSizes.map((size) => (
                <Badge
                  key={size}
                  variant={selectedSizes.includes(size) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-gray-100"
                  onClick={() => setSelectedSizes(prev => prev.includes(size) ? prev.filter(s => s !== size) : [...prev, size])}
                >
                  {size}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Cost & Fees */}
      <Card>
        <CardHeader>
          <CardTitle>Cost & Fees</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="costPrice">Cost Price (M) *</Label>
              <Input
                id="costPrice"
                name="costPrice"
                type="number"
                step="0.01"
                min="0"
                value={feeForm.costPrice}
                onChange={handleFeeChange}
                placeholder="0.00"
                required
              />
            </div>
            <div>
              <Label htmlFor="shippingFee">Shipping Fee (M)</Label>
              <Input
                id="shippingFee"
                name="shippingFee"
                type="number"
                step="0.01"
                min="0"
                value={feeForm.shippingFee}
                onChange={handleFeeChange}
                placeholder={defaultFees.shippingFee || "100"}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="lateCollectionFee">Late Collection Fee (M10)</Label>
              <div className="flex items-center gap-2">
                <Switch
                  id="lateCollectionFee"
                  checked={feeForm.lateCollectionFee}
                  onCheckedChange={handleLateFeeToggle}
                />
                <span>{feeForm.lateCollectionFee ? "Applied" : "Not Applied"}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Image Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Product Images *
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {images.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {images.map((image, index) => (
                <div key={index} className="relative group">
                  <img
                    src={image}
                    alt={`Product image ${index + 1}`}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
          <UploadDropzone
            endpoint="productImageUploader"
            onClientUploadComplete={(res) => {
              setLastUploadResponse(res);
              if (res && Array.isArray(res)) {
                handleImageUpload(res);
              } else {
                alert("Upload completed but response format was unexpected. Please ensure you are logged in as an admin and try again.");
              }
            }}
            onUploadError={(error: Error) => {
              alert(`Upload failed: ${error.message}. Please ensure you are logged in as an admin and try again.`);
            }}
            onUploadBegin={() => {
              // Optionally clear errors
            }}
            className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
          />
        </CardContent>
      </Card>
      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {/* Submit Button */}
      <div className="flex justify-end gap-4">
        <Button type="submit" disabled={loading}>
          {loading ? (mode === "edit" ? "Updating..." : "Creating Product...") : (mode === "edit" ? "Update Product" : "Create Product")}
        </Button>
      </div>
    </form>
  );
} 