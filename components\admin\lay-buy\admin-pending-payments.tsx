"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  CreditCard,
  CheckCircle,
  XCircle,
  Eye,
  ExternalLink,
  User,
  Calendar,
  RefreshCw,
  AlertTriangle,
} from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/lib/product-utils";
import { formatLayBuyPrice } from "@/lib/lay-buy-utils";

interface PendingPayment {
  id: string;
  amount: number;
  paymentType: string;
  paymentMethod?: string;
  paymentProof?: string;
  notes?: string;
  createdAt: string;
  order: {
    id: string;
    orderNumber: string;
    totalAmount: number;
    amountPaid: number;
    customer: {
      id: string;
      name: string;
      email: string;
    };
  };
}

export default function AdminPendingPayments() {
  const [payments, setPayments] = useState<PendingPayment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<PendingPayment | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [showDialog, setShowDialog] = useState(false);
  const [actionType, setActionType] = useState<"VERIFIED" | "REJECTED" | null>(null);

  useEffect(() => {
    fetchPendingPayments();
  }, []);

  const fetchPendingPayments = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/lay-buy-payments");
      const result = await response.json();

      if (result.success) {
        setPayments(result.data.payments);
      }
    } catch (error) {
      console.error("Error fetching pending payments:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentAction = (payment: PendingPayment, action: "VERIFIED" | "REJECTED") => {
    setSelectedPayment(payment);
    setActionType(action);
    setAdminNotes("");
    setShowDialog(true);
  };

  const confirmPaymentAction = async () => {
    if (!selectedPayment || !actionType) return;

    setIsProcessing(true);
    try {
      const response = await fetch("/api/admin/lay-buy-payments", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          paymentId: selectedPayment.id,
          status: actionType,
          adminNotes: adminNotes.trim() || undefined,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Remove the processed payment from the list
        setPayments(prev => prev.filter(p => p.id !== selectedPayment.id));
        setShowDialog(false);
        setSelectedPayment(null);
        setActionType(null);
        setAdminNotes("");
      } else {
        console.error("Failed to process payment:", result.error);
        // You might want to show an error toast here
      }
    } catch (error) {
      console.error("Error processing payment:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getPaymentTypeBadge = (type: string) => {
    const typeConfig = {
      UPFRONT: { color: "bg-blue-100 text-blue-800", label: "Upfront" },
      INSTALLMENT: { color: "bg-yellow-100 text-yellow-800", label: "Installment" },
      COMPLETION: { color: "bg-green-100 text-green-800", label: "Completion" },
    };
    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.INSTALLMENT;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Pending Payments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Pending Payments ({payments.length})
            </CardTitle>
            <Button onClick={fetchPendingPayments} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {payments.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">All Caught Up!</h3>
              <p className="text-gray-600">No pending payments to review at the moment.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {payments.map((payment) => (
                <div key={payment.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-lg">{formatLayBuyPrice(payment.amount)}</h3>
                        {getPaymentTypeBadge(payment.paymentType)}
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <User className="h-4 w-4" />
                        <span>{payment.order.customer.name}</span>
                        <span>•</span>
                        <span>{payment.order.customer.email}</span>
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Calendar className="h-4 w-4" />
                        <span>Submitted: {new Date(payment.createdAt).toLocaleDateString()}</span>
                        {payment.paymentMethod && (
                          <>
                            <span>•</span>
                            <span>Method: {payment.paymentMethod}</span>
                          </>
                        )}
                      </div>

                      <div className="text-sm text-gray-600">
                        Order: <strong>{payment.order.orderNumber}</strong> • 
                        Balance: {formatLayBuyPrice((payment.order.totalAmount ?? 0) - (payment.order.amountPaid ?? 0))}
                      </div>

                      {payment.notes && (
                        <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                          <strong>Customer Notes:</strong> {payment.notes}
                        </div>
                      )}

                      {payment.paymentProof && (
                        <div className="flex items-center gap-2">
                          <a
                            href={payment.paymentProof}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
                          >
                            <ExternalLink className="h-3 w-3" />
                            View Payment Proof
                          </a>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/lay-buy-orders/${payment.order.id}`}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Order
                        </Link>
                      </Button>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2 border-t">
                    <Button
                      onClick={() => handlePaymentAction(payment, "VERIFIED")}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Verify Payment
                    </Button>
                    <Button
                      onClick={() => handlePaymentAction(payment, "REJECTED")}
                      variant="destructive"
                      className="flex-1"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject Payment
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {actionType === "VERIFIED" ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              {actionType === "VERIFIED" ? "Verify Payment" : "Reject Payment"}
            </DialogTitle>
            <DialogDescription>
              {selectedPayment && (
                <>
                  <p>
                    {actionType === "VERIFIED" ? "Verify" : "Reject"} payment of{" "}
                    <strong>{formatLayBuyPrice(selectedPayment.amount)}</strong> for order{" "}
                    <strong>{selectedPayment.order.orderNumber}</strong>?
                  </p>
                  {actionType === "VERIFIED" && (
                    <div>
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          This will add the payment amount to the customer's order balance and may complete their order if fully paid.
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin Notes (Optional)
              </label>
              <Textarea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder={`Add notes about this ${actionType?.toLowerCase()} action...`}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={confirmPaymentAction}
              disabled={isProcessing}
              className={actionType === "VERIFIED" ? "bg-green-600 hover:bg-green-700" : ""}
              variant={actionType === "REJECTED" ? "destructive" : "default"}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  {actionType === "VERIFIED" ? (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  ) : (
                    <XCircle className="h-4 w-4 mr-2" />
                  )}
                  {actionType === "VERIFIED" ? "Verify Payment" : "Reject Payment"}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
