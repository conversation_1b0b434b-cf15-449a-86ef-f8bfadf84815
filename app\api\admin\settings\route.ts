import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET: Fetch global settings
export async function GET() {
  let settings = await prisma.settings.findFirst();
  if (!settings) {
    // Create default settings if not present
    settings = await prisma.settings.create({
      data: {
        primaryColor: "#3b82f6",
        defaultShippingFee: 100,
        defaultLateCollectionFee: 10,
        defaultDeliveryFee: 90,
        defaultBulkDeliveryFee: 60,
      },
    });
  }
  return NextResponse.json({ success: true, data: settings });
}

// POST: Update global settings
export async function POST(req: NextRequest) {
  const body = await req.json();
  const { primaryColor, feeSettings } = body;
  let settings = await prisma.settings.findFirst();
  if (!settings) {
    settings = await prisma.settings.create({
      data: {
        primaryColor: primaryColor || "#3b82f6",
        defaultShippingFee: feeSettings?.defaultShippingFee ?? 100,
        defaultLateCollectionFee: feeSettings?.defaultLateCollectionFee ?? 10,
        defaultDeliveryFee: feeSettings?.defaultDeliveryFee ?? 90,
        defaultBulkDeliveryFee: feeSettings?.defaultBulkDeliveryFee ?? 60,
      },
    });
  } else {
    settings = await prisma.settings.update({
      where: { id: settings.id },
      data: {
        primaryColor: primaryColor || settings.primaryColor,
        defaultShippingFee: feeSettings?.defaultShippingFee ?? settings.defaultShippingFee,
        defaultLateCollectionFee: feeSettings?.defaultLateCollectionFee ?? settings.defaultLateCollectionFee,
        defaultDeliveryFee: feeSettings?.defaultDeliveryFee ?? settings.defaultDeliveryFee,
        defaultBulkDeliveryFee: feeSettings?.defaultBulkDeliveryFee ?? settings.defaultBulkDeliveryFee,
      },
    });
  }
  return NextResponse.json({ success: true, data: settings });
} 