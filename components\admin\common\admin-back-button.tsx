"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";

interface AdminBackButtonProps {
  href?: string;
  label?: string;
}

export default function AdminBackButton({ 
  href = "/admin",
  label = "Back"
}: AdminBackButtonProps) {
  const router = useRouter();

  return (
    <Button
      variant="outline"
      size="sm"
      className="mb-4"
      onClick={() => router.push(href)}
    >
      <ArrowLeft className="w-4 h-4 mr-2" />
      {label}
    </Button>
  );
}
