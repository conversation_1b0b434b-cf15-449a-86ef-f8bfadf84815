"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  DollarSign, 
  Package, 
  AlertCircle, 
  CheckCircle, 
  Save,
  TrendingUp,
  Calculator
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";

interface ProductWithOrders {
  id: string;
  name: string;
  brand: string;
  price: number;
  costPrice: number | null;
  shippingFee: number | null;
  lateCollectionFee: number | null;
  totalCost: number | null;
  costPriceUpdatedAt: string | null;
  costPriceUpdatedBy: string | null;
  feesUpdatedAt: string | null;
  feesUpdatedBy: string | null;
  images: string[];
  orderCount: number;
  totalRevenue: number;
  potentialProfit: number | null;
}

interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  order: {
    id: string;
    orderNumber: string;
    createdAt: string;
    user: {
      name: string;
    };
  };
}

export default function CostPricesPage() {
  const [products, setProducts] = useState<ProductWithOrders[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<string | null>(null);
  const [costPrices, setCostPrices] = useState<Record<string, string>>({});
  const [shippingFees, setShippingFees] = useState<Record<string, string>>({});
  const [lateCollectionFees, setLateCollectionFees] = useState<Record<string, string>>({});
  const [lateCollectionEnabled, setLateCollectionEnabled] = useState<Record<string, boolean>>({});
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [stats, setStats] = useState({
    totalProducts: 0,
    productsWithCostPrice: 0,
    pendingCostPrices: 0,
    totalPendingRevenue: 0,
  });

  useEffect(() => {
    fetchPendingCostPrices();
  }, []);

  const fetchPendingCostPrices = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/cost-prices');
      if (!response.ok) throw new Error('Failed to fetch cost prices');
      
      const data = await response.json();

      // Handle API response structure
      const products = data.success ? data.data.products : [];
      const stats = data.success ? data.data.stats : {
        totalProducts: 0,
        productsWithCostPrice: 0,
        pendingCostPrices: 0,
        totalPendingRevenue: 0,
      };

      setProducts(products);
      setStats(stats);

      // Initialize form inputs
      const initialCostPrices: Record<string, string> = {};
      const initialShippingFees: Record<string, string> = {};
      const initialLateCollectionFees: Record<string, string> = {};
      const initialLateCollectionEnabled: Record<string, boolean> = {};

      products.forEach((product: ProductWithOrders) => {
        if (product.costPrice) {
          initialCostPrices[product.id] = product.costPrice.toString();
        }
        // Set default shipping fee to M100 if not set
        initialShippingFees[product.id] = product.shippingFee?.toString() || "100";
        // Set default late collection fee to M10 if not set
        initialLateCollectionFees[product.id] = product.lateCollectionFee?.toString() || "10";
        // Enable late collection fee if it was previously set
        initialLateCollectionEnabled[product.id] = product.lateCollectionFee !== null;
      });

      setCostPrices(initialCostPrices);
      setShippingFees(initialShippingFees);
      setLateCollectionFees(initialLateCollectionFees);
      setLateCollectionEnabled(initialLateCollectionEnabled);
    } catch (error) {
      console.error('Error fetching cost prices:', error);
      setMessage({ type: 'error', text: 'Failed to load cost prices' });
    } finally {
      setLoading(false);
    }
  };

  const updateCostPrice = async (productId: string) => {
    const costPrice = parseFloat(costPrices[productId]);
    const shippingFee = parseFloat(shippingFees[productId]);
    const lateCollectionFee = lateCollectionEnabled[productId] ? parseFloat(lateCollectionFees[productId]) : 0;

    if (!costPrice || costPrice <= 0) {
      setMessage({ type: 'error', text: 'Please enter a valid cost price' });
      return;
    }

    if (!shippingFee || shippingFee < 0) {
      setMessage({ type: 'error', text: 'Please enter a valid shipping fee' });
      return;
    }

    if (lateCollectionEnabled[productId] && (!lateCollectionFee || lateCollectionFee < 0)) {
      setMessage({ type: 'error', text: 'Please enter a valid late collection fee' });
      return;
    }

    try {
      setSaving(productId);
      const response = await fetch('/api/admin/cost-prices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId,
          costPrice,
          shippingFee,
          lateCollectionFee: lateCollectionEnabled[productId] ? lateCollectionFee : null
        }),
      });

      if (!response.ok) throw new Error('Failed to update cost price and fees');

      setMessage({ type: 'success', text: 'Cost price and fees updated successfully!' });
      await fetchPendingCostPrices(); // Refresh data
    } catch (error) {
      console.error('Error updating cost price and fees:', error);
      setMessage({ type: 'error', text: 'Failed to update cost price and fees' });
    } finally {
      setSaving(null);
    }
  };

  const handleCostPriceChange = (productId: string, value: string) => {
    setCostPrices(prev => ({ ...prev, [productId]: value }));
  };

  const handleShippingFeeChange = (productId: string, value: string) => {
    setShippingFees(prev => ({ ...prev, [productId]: value }));
  };

  const handleLateCollectionFeeChange = (productId: string, value: string) => {
    setLateCollectionFees(prev => ({ ...prev, [productId]: value }));
  };

  const handleLateCollectionToggle = (productId: string, enabled: boolean) => {
    setLateCollectionEnabled(prev => ({ ...prev, [productId]: enabled }));
  };

  const calculateTotalCost = (productId: string) => {
    const costPrice = parseFloat(costPrices[productId]) || 0;
    const shippingFee = parseFloat(shippingFees[productId]) || 0;
    const lateCollectionFee = lateCollectionEnabled[productId] ? (parseFloat(lateCollectionFees[productId]) || 0) : 0;
    return costPrice + shippingFee + lateCollectionFee;
  };

  const calculatePotentialProfit = (product: ProductWithOrders, productId: string) => {
    const totalCost = calculateTotalCost(productId);
    return (product.price - totalCost) * product.orderCount;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading cost prices...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Cost Price Management</h1>
        <p className="text-gray-600">
          Manage product cost prices to enable profit calculations and revenue tracking.
        </p>
      </div>

      {message && (
        <Alert className={`mb-6 ${message.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className={message.type === 'success' ? 'text-green-700' : 'text-red-700'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Products</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalProducts}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">With Cost Price</p>
                <p className="text-3xl font-bold text-green-600">{stats.productsWithCostPrice}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Cost Prices</p>
                <p className="text-3xl font-bold text-orange-600">{stats.pendingCostPrices}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Revenue</p>
                <p className="text-3xl font-bold text-purple-600">M{stats.totalPendingRevenue.toLocaleString()}</p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Products List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Products Requiring Cost Price Input
          </CardTitle>
        </CardHeader>
        <CardContent>
          {products.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">All Set!</h3>
              <p className="text-gray-600">All products have cost prices assigned.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <div key={product.id} className="border rounded-lg p-6">
                  <div className="flex items-start gap-4">
                    {product.images[0] && (
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="w-20 h-20 object-cover rounded-lg"
                      />
                    )}
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                          <p className="text-gray-600">{product.brand}</p>
                          <div className="flex items-center gap-4 mt-2">
                            <span className="text-sm text-gray-500">
                              Sale Price: <span className="font-medium">{formatPrice(product.price)}</span>
                            </span>
                            <span className="text-sm text-gray-500">
                              Orders: <span className="font-medium">{product.orderCount}</span>
                            </span>
                            <span className="text-sm text-gray-500">
                              Revenue: <span className="font-medium">{formatPrice(product.totalRevenue)}</span>
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 flex-wrap">
                          {product.totalCost ? (
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              Total Cost: {formatPrice(product.totalCost)}
                            </Badge>
                          ) : (
                            <Badge variant="destructive">
                              No Cost Data
                            </Badge>
                          )}
                          {product.costPrice && (
                            <Badge variant="outline" className="text-xs">
                              Base: {formatPrice(product.costPrice)}
                            </Badge>
                          )}
                          {product.shippingFee && (
                            <Badge variant="outline" className="text-xs">
                              Shipping: {formatPrice(product.shippingFee)}
                            </Badge>
                          )}
                          {product.lateCollectionFee && (
                            <Badge variant="outline" className="text-xs">
                              Late Fee: {formatPrice(product.lateCollectionFee)}
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="mt-4 space-y-4">
                        {/* Cost Price Input */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label htmlFor={`cost-${product.id}`} className="text-sm font-medium text-gray-700 block mb-1">
                              Base Cost Price (M):
                            </label>
                            <Input
                              id={`cost-${product.id}`}
                              type="number"
                              step="0.01"
                              min="0"
                              placeholder="0.00"
                              value={costPrices[product.id] || ''}
                              onChange={(e) => handleCostPriceChange(product.id, e.target.value)}
                              className="w-full"
                            />
                          </div>

                          <div>
                            <label htmlFor={`shipping-${product.id}`} className="text-sm font-medium text-gray-700 block mb-1">
                              Shipping Fee (M):
                            </label>
                            <Input
                              id={`shipping-${product.id}`}
                              type="number"
                              step="0.01"
                              min="0"
                              placeholder="100.00"
                              value={shippingFees[product.id] || ''}
                              onChange={(e) => handleShippingFeeChange(product.id, e.target.value)}
                              className="w-full"
                            />
                            <p className="text-xs text-gray-500 mt-1">Default: M100</p>
                          </div>

                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <input
                                type="checkbox"
                                id={`late-enabled-${product.id}`}
                                checked={lateCollectionEnabled[product.id] || false}
                                onChange={(e) => handleLateCollectionToggle(product.id, e.target.checked)}
                                className="rounded"
                              />
                              <label htmlFor={`late-enabled-${product.id}`} className="text-sm font-medium text-gray-700">
                                Late Collection Fee (M):
                              </label>
                            </div>
                            <Input
                              id={`late-${product.id}`}
                              type="number"
                              step="0.01"
                              min="0"
                              placeholder="10.00"
                              value={lateCollectionFees[product.id] || ''}
                              onChange={(e) => handleLateCollectionFeeChange(product.id, e.target.value)}
                              disabled={!lateCollectionEnabled[product.id]}
                              className="w-full"
                            />
                            <p className="text-xs text-gray-500 mt-1">Default: M10 (optional)</p>
                          </div>
                        </div>

                        {/* Summary and Action */}
                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="space-y-1">
                            {costPrices[product.id] && parseFloat(costPrices[product.id]) > 0 && (
                              <>
                                <div className="text-sm text-gray-600">
                                  Total Cost: <span className="font-medium text-blue-600">
                                    {formatPrice(calculateTotalCost(product.id))}
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  Potential Profit: <span className="font-medium text-green-600">
                                    {formatPrice(calculatePotentialProfit(product, product.id))}
                                  </span>
                                </div>
                              </>
                            )}
                          </div>

                          <Button
                            onClick={() => updateCostPrice(product.id)}
                            disabled={!costPrices[product.id] || parseFloat(costPrices[product.id]) <= 0 || saving === product.id}
                          >
                            {saving === product.id ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className="h-4 w-4 mr-2" />
                                Save Cost & Fees
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
