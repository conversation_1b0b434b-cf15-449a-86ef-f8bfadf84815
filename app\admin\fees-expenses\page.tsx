"use client";

import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import { User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  DollarSign, 
  Package, 
  Truck, 
  AlertCircle, 
  TrendingUp,
  TrendingDown,
  Download,
  Calculator,
  CheckCircle,
  Clock,
  Users,
  ShoppingCart
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";

interface OrderWithFees {
  id: string;
  orderNumber: string;
  customerName: string;
  status: string;
  createdAt: string;
  deliveredAt: string | null;
  totalAmount: number;
  totalCostPrice: number;
  totalProfit: number;
  shippingFees: number;
  deliveryFee: number;
  lateCollectionFees: number;
  bulkDeliveryDiscount: number;
  isBulkDelivery: boolean;
  deliveryFeePaid: boolean;
  deliveryFeePaidAt: string | null;
  shoesCount: number;
  items: Array<{
    id: string;
    productName: string;
    productBrand: string;
    quantity: number;
    price: number;
    costPrice: number;
    shippingFee: number;
    lateCollectionFee: number;
    totalCost: number;
    profit: number;
  }>;
}

interface FeeStats {
  totalRevenue: number;
  totalCostPrice: number;
  totalProfit: number;
  totalShippingFees: number;
  totalDeliveryFees: number;
  totalLateCollectionFees: number;
  totalBulkDeliveryDiscounts: number;
  totalFeesPaid: number;
  totalFeesOwed: number;
  totalFeesOverall: number;
  netProfitAfterFees: number;
  ordersCount: number;
  deliveredOrdersCount: number;
  bulkDeliveriesCount: number;
}

export default function AdminFeesExpensesPage() {
  return (
    <AdminRoute>
      <AdminFeesExpensesPageContent />
    </AdminRoute>
  );
}

function AdminFeesExpensesPageContent() {
  const { data: session } = useSession();
  const [userWithRole, setUserWithRole] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState<OrderWithFees[]>([]);
  const [stats, setStats] = useState<FeeStats | null>(null);
  const [exporting, setExporting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const user = session!.user;

  useEffect(() => {
    const getUserDetails = async () => {
      if (user) {
        try {
          const userResponse = await getUserById(user.id);
          if (userResponse.success && userResponse.data) {
            setUserWithRole(userResponse.data as User);
          }
        } catch (error) {
          console.error("Error fetching user details:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    getUserDetails();
  }, [user]);

  useEffect(() => {
    if (userWithRole) {
      fetchFeesExpenses();
    }
  }, [userWithRole]);

  const fetchFeesExpenses = async () => {
    try {
      const response = await fetch('/api/admin/fees-expenses');
      if (!response.ok) throw new Error('Failed to fetch fees and expenses');
      
      const data = await response.json();

      if (data.success) {
        setOrders(data.data.orders);
        setStats(data.data.stats);
      } else {
        throw new Error(data.error || 'Failed to fetch data');
      }
    } catch (error) {
      console.error('Error fetching fees and expenses:', error);
      setMessage({ type: 'error', text: 'Failed to load fees and expenses data' });
    }
  };

  const exportFeesReport = async () => {
    try {
      setExporting(true);
      const response = await fetch('/api/admin/fees-expenses/export');
      if (!response.ok) throw new Error('Failed to export report');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `fees-expenses-report-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setMessage({ type: 'success', text: 'Fees and expenses report exported successfully!' });
    } catch (error) {
      console.error('Error exporting report:', error);
      setMessage({ type: 'error', text: 'Failed to export report' });
    } finally {
      setExporting(false);
    }
  };

  if (isLoading || !userWithRole) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <AdminLayout user={userWithRole}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Fees & Expenses Management</h1>
              <p className="text-gray-600">
                Comprehensive overview of all business fees, delivery costs, and profit calculations.
              </p>
            </div>
            <Button onClick={exportFeesReport} disabled={exporting}>
              {exporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export Report
                </>
              )}
            </Button>
          </div>
        </div>

        {message && (
          <Alert className={`mb-6 ${message.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>
            <AlertDescription className={message.type === 'success' ? 'text-green-700' : 'text-red-700'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {stats && (
          <>
            {/* Main Financial Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                      <p className="text-3xl font-bold text-green-600">M{stats.totalRevenue.toLocaleString()}</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Net Profit After Fees</p>
                      <p className="text-3xl font-bold text-blue-600">M{stats.netProfitAfterFees.toLocaleString()}</p>
                    </div>
                    <Calculator className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Fees Overall</p>
                      <p className="text-3xl font-bold text-red-600">M{stats.totalFeesOverall.toLocaleString()}</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Orders</p>
                      <p className="text-3xl font-bold text-purple-600">{stats.ordersCount}</p>
                    </div>
                    <ShoppingCart className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Fee Breakdown */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Shipping Fees</p>
                      <p className="text-2xl font-bold text-orange-600">M{stats.totalShippingFees.toLocaleString()}</p>
                    </div>
                    <Package className="h-6 w-6 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Delivery Fees</p>
                      <p className="text-2xl font-bold text-indigo-600">M{stats.totalDeliveryFees.toLocaleString()}</p>
                    </div>
                    <Truck className="h-6 w-6 text-indigo-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Late Collection Fees</p>
                      <p className="text-2xl font-bold text-yellow-600">M{stats.totalLateCollectionFees.toLocaleString()}</p>
                    </div>
                    <AlertCircle className="h-6 w-6 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Bulk Delivery Savings</p>
                      <p className="text-2xl font-bold text-green-600">M{stats.totalBulkDeliveryDiscounts.toLocaleString()}</p>
                    </div>
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Delivery Fee Status */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Delivery Fees Paid</p>
                      <p className="text-2xl font-bold text-green-600">M{stats.totalFeesPaid.toLocaleString()}</p>
                    </div>
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Delivery Fees Owed</p>
                      <p className="text-2xl font-bold text-red-600">M{stats.totalFeesOwed.toLocaleString()}</p>
                    </div>
                    <Clock className="h-6 w-6 text-red-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Bulk Deliveries</p>
                      <p className="text-2xl font-bold text-purple-600">{stats.bulkDeliveriesCount}</p>
                    </div>
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}

        {/* Orders with Fee Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Orders with Fee Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            {orders.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Orders Found</h3>
                <p className="text-gray-600">No orders have been placed yet.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {orders.map((order) => (
                  <div key={order.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-4">
                        <div>
                          <h3 className="font-semibold text-gray-900">{order.orderNumber}</h3>
                          <p className="text-sm text-gray-600">{order.customerName}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={order.isBulkDelivery ? "secondary" : "outline"}>
                            {order.isBulkDelivery ? "Bulk Delivery" : "Regular Delivery"}
                          </Badge>
                          <Badge variant="outline">
                            {order.shoesCount} shoe{order.shoesCount !== 1 ? 's' : ''}
                          </Badge>
                          <Badge variant={order.deliveryFeePaid ? "secondary" : "destructive"}>
                            {order.deliveryFeePaid ? "Paid" : "Unpaid"}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-semibold text-gray-900">M{order.totalAmount.toLocaleString()}</p>
                        <p className="text-sm text-gray-600">Total Revenue</p>
                      </div>
                    </div>

                    {/* Fee Breakdown */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Shipping Fees</p>
                        <p className="font-medium">M{order.shippingFees.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Delivery Fee</p>
                        <p className="font-medium">M{order.deliveryFee.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Late Collection</p>
                        <p className="font-medium">M{order.lateCollectionFees.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Bulk Discount</p>
                        <p className="font-medium text-green-600">-M{order.bulkDeliveryDiscount.toLocaleString()}</p>
                      </div>
                    </div>

                    {/* Profit Summary */}
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="flex justify-between items-center">
                        <div className="text-sm">
                          <p className="text-gray-600">Total Cost: M{order.totalCostPrice.toLocaleString()}</p>
                          <p className="text-gray-600">Net Profit: M{order.totalProfit.toLocaleString()}</p>
                        </div>
                        <div className="text-right">
                          <p className={`text-lg font-semibold ${order.totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            M{order.totalProfit.toLocaleString()}
                          </p>
                          <p className="text-sm text-gray-600">Profit</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
} 