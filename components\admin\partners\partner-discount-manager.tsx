"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Handshake, 
  Plus, 
  Copy, 
  CheckCircle, 
  AlertTriangle,
  Percent,
  DollarSign
} from "lucide-react";

interface PartnerDiscount {
  id: string;
  code: string;
  partnerName: string;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT';
  value: number;
  maxUses?: number;
  usedCount: number;
  isActive: boolean;
  validUntil?: string;
  createdAt: string;
}

export default function PartnerDiscountManager() {
  const [discounts, setDiscounts] = useState<PartnerDiscount[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    partnerName: '',
    type: 'PERCENTAGE' as 'PERCENTAGE' | 'FIXED_AMOUNT',
    value: '',
    maxUses: '',
    validUntil: '',
  });

  // Fetch existing partner discounts
  useEffect(() => {
    fetchPartnerDiscounts();
  }, []);

  const fetchPartnerDiscounts = async () => {
    try {
      const response = await fetch('/api/admin/partner-discounts');
      const data = await response.json();
      if (data.success) {
        setDiscounts(data.data);
      }
    } catch (error) {
      console.error('Error fetching partner discounts:', error);
    }
  };

  const handleCreateDiscount = async () => {
    if (!formData.partnerName || !formData.value) {
      alert('Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/partner-discounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          partnerName: formData.partnerName,
          type: formData.type,
          value: parseFloat(formData.value),
          maxUses: formData.maxUses ? parseInt(formData.maxUses) : undefined,
          validUntil: formData.validUntil || undefined,
        }),
      });

      const result = await response.json();
      if (result.success) {
        setDiscounts([result.data, ...discounts]);
        setFormData({
          partnerName: '',
          type: 'PERCENTAGE',
          value: '',
          maxUses: '',
          validUntil: '',
        });
        setShowCreateForm(false);
      } else {
        alert(result.error || 'Failed to create discount code');
      }
    } catch (error) {
      console.error('Error creating discount:', error);
      alert('Failed to create discount code');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const handleToggleStatus = async (discountId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/partner-discounts/${discountId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !isActive }),
      });

      const result = await response.json();
      if (result.success) {
        setDiscounts(discounts.map(d => 
          d.id === discountId ? { ...d, isActive: !isActive } : d
        ));
      }
    } catch (error) {
      console.error('Error updating discount status:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Handshake className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">Partner Discount Manager</h2>
        </div>
        <Button onClick={() => setShowCreateForm(!showCreateForm)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Partner Discount
        </Button>
      </div>

      {/* Create Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Partner Discount</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="partnerName">Partner Name *</Label>
                <Input
                  id="partnerName"
                  value={formData.partnerName}
                  onChange={(e) => setFormData({ ...formData, partnerName: e.target.value })}
                  placeholder="Enter partner name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Discount Type *</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: 'PERCENTAGE' | 'FIXED_AMOUNT') => 
                    setFormData({ ...formData, type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                    <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="value">
                  {formData.type === 'PERCENTAGE' ? 'Percentage (%)' : 'Amount (M)'} *
                </Label>
                <Input
                  id="value"
                  type="number"
                  value={formData.value}
                  onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                  placeholder={formData.type === 'PERCENTAGE' ? '10' : '50'}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxUses">Max Uses (Optional)</Label>
                <Input
                  id="maxUses"
                  type="number"
                  value={formData.maxUses}
                  onChange={(e) => setFormData({ ...formData, maxUses: e.target.value })}
                  placeholder="Unlimited"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="validUntil">Valid Until (Optional)</Label>
                <Input
                  id="validUntil"
                  type="date"
                  value={formData.validUntil}
                  onChange={(e) => setFormData({ ...formData, validUntil: e.target.value })}
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={handleCreateDiscount} disabled={isLoading}>
                {isLoading ? 'Creating...' : 'Create Discount'}
              </Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Existing Discounts */}
      <Card>
        <CardHeader>
          <CardTitle>Existing Partner Discounts</CardTitle>
        </CardHeader>
        <CardContent>
          {discounts.length === 0 ? (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                No partner discounts created yet. Create your first one above.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {discounts.map((discount) => (
                <div key={discount.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold">{discount.partnerName}</h3>
                        <Badge variant={discount.isActive ? "default" : "secondary"}>
                          {discount.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <strong>Code:</strong> {discount.code}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyCode(discount.code)}
                            className="h-6 w-6 p-0"
                          >
                            {copiedCode === discount.code ? (
                              <CheckCircle className="h-3 w-3 text-green-600" />
                            ) : (
                              <Copy className="h-3 w-3" />
                            )}
                          </Button>
                        </span>
                        <span className="flex items-center gap-1">
                          {discount.type === 'PERCENTAGE' ? (
                            <Percent className="h-3 w-3" />
                          ) : (
                            <DollarSign className="h-3 w-3" />
                          )}
                          {discount.value}{discount.type === 'PERCENTAGE' ? '%' : ' M'}
                        </span>
                        <span>Used: {discount.usedCount}/{discount.maxUses || '∞'}</span>
                      </div>
                    </div>
                    <Button
                      variant={discount.isActive ? "destructive" : "default"}
                      size="sm"
                      onClick={() => handleToggleStatus(discount.id, discount.isActive)}
                    >
                      {discount.isActive ? "Deactivate" : "Activate"}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
