import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { ApiResponse } from "@/utils/types";

// GET /api/admin/fee-settings - Get current fee settings
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    // Get current settings from the database
    const settings = await prisma.settings.findFirst();

    const defaultSettings = {
      defaultShippingFee: 100, // M100 per shoe
      defaultLateCollectionFee: 10, // M10
      defaultDeliveryFee: 90, // M90 per shoe
      defaultBulkDeliveryFee: 60, // M60 per shoe for bulk (5+ shoes)
      bulkDeliveryThreshold: 5, // Number of shoes to qualify for bulk
    };

    const currentSettings = settings ? {
      defaultShippingFee: settings.defaultShippingFee || defaultSettings.defaultShippingFee,
      defaultLateCollectionFee: settings.defaultLateCollectionFee || defaultSettings.defaultLateCollectionFee,
      defaultDeliveryFee: settings.defaultDeliveryFee || defaultSettings.defaultDeliveryFee,
      defaultBulkDeliveryFee: settings.defaultBulkDeliveryFee || defaultSettings.defaultBulkDeliveryFee,
      bulkDeliveryThreshold: settings.bulkDeliveryThreshold || defaultSettings.bulkDeliveryThreshold,
    } : defaultSettings;

    const response: ApiResponse<typeof currentSettings> = {
      success: true,
      data: currentSettings,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching fee settings:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch fee settings" },
      { status: 500 }
    );
  }
}

// POST /api/admin/fee-settings - Update fee settings
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      defaultShippingFee,
      defaultLateCollectionFee,
      defaultDeliveryFee,
      defaultBulkDeliveryFee,
      bulkDeliveryThreshold,
    } = body;

    // Validate inputs
    if (defaultShippingFee !== undefined && (defaultShippingFee < 0)) {
      return NextResponse.json(
        { success: false, error: "Default shipping fee must be non-negative" },
        { status: 400 }
      );
    }

    if (defaultLateCollectionFee !== undefined && (defaultLateCollectionFee < 0)) {
      return NextResponse.json(
        { success: false, error: "Default late collection fee must be non-negative" },
        { status: 400 }
      );
    }

    if (defaultDeliveryFee !== undefined && (defaultDeliveryFee < 0)) {
      return NextResponse.json(
        { success: false, error: "Default delivery fee must be non-negative" },
        { status: 400 }
      );
    }

    if (defaultBulkDeliveryFee !== undefined && (defaultBulkDeliveryFee < 0)) {
      return NextResponse.json(
        { success: false, error: "Default bulk delivery fee must be non-negative" },
        { status: 400 }
      );
    }

    if (bulkDeliveryThreshold !== undefined && (bulkDeliveryThreshold < 1)) {
      return NextResponse.json(
        { success: false, error: "Bulk delivery threshold must be at least 1" },
        { status: 400 }
      );
    }

    // Get current settings first
    const currentSettings = await prisma.settings.findFirst();
    
    // Update or create settings
    const updatedSettings = await prisma.settings.upsert({
      where: { id: currentSettings?.id || "default" },
      update: {
        defaultShippingFee: defaultShippingFee !== undefined ? defaultShippingFee : undefined,
        defaultLateCollectionFee: defaultLateCollectionFee !== undefined ? defaultLateCollectionFee : undefined,
        defaultDeliveryFee: defaultDeliveryFee !== undefined ? defaultDeliveryFee : undefined,
        defaultBulkDeliveryFee: defaultBulkDeliveryFee !== undefined ? defaultBulkDeliveryFee : undefined,
        bulkDeliveryThreshold: bulkDeliveryThreshold !== undefined ? bulkDeliveryThreshold : undefined,
        updatedAt: new Date(),
      },
      create: {
        defaultShippingFee: defaultShippingFee || 100,
        defaultLateCollectionFee: defaultLateCollectionFee || 10,
        defaultDeliveryFee: defaultDeliveryFee || 90,
        defaultBulkDeliveryFee: defaultBulkDeliveryFee || 60,
        bulkDeliveryThreshold: bulkDeliveryThreshold || 5,
      },
    });

    const response: ApiResponse<typeof updatedSettings> = {
      success: true,
      data: updatedSettings,
      message: "Fee settings updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating fee settings:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update fee settings" },
      { status: 500 }
    );
  }
} 