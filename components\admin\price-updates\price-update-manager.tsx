"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  DollarSign, 
  AlertTriangle, 
  CheckCircle, 
  Play,
  Info,
  Calculator
} from "lucide-react";

interface PriceUpdateRule {
  condition: string;
  increase: string;
  example: string;
}

interface PriceUpdateInfo {
  description: string;
  rules: PriceUpdateRule[];
  notes: string[];
}

export default function PriceUpdateManager() {
  const [isLoading, setIsLoading] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<PriceUpdateInfo | null>(null);
  const [batchSize, setBatchSize] = useState(50);
  const [dryRun, setDryRun] = useState(true);
  const [lastResult, setLastResult] = useState<any>(null);

  // Fetch price update information
  useEffect(() => {
    const fetchUpdateInfo = async () => {
      try {
        const response = await fetch('/api/admin/price-updates');
        const data = await response.json();
        if (data.success) {
          setUpdateInfo(data.priceUpdateRules);
        }
      } catch (error) {
        console.error('Error fetching price update info:', error);
      }
    };

    fetchUpdateInfo();
  }, []);

  const handlePriceUpdate = async () => {
    setIsLoading(true);
    setLastResult(null);

    try {
      const response = await fetch('/api/admin/price-updates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dryRun,
          batchSize,
        }),
      });

      const result = await response.json();
      setLastResult(result);

      if (result.success) {
        // Show success message
        console.log('Price update started:', result);
      } else {
        console.error('Price update failed:', result.error);
      }
    } catch (error) {
      console.error('Error starting price update:', error);
      setLastResult({
        success: false,
        error: 'Failed to start price update process'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <TrendingUp className="h-6 w-6 text-blue-600" />
        <h2 className="text-2xl font-bold text-gray-900">Price Update Manager</h2>
      </div>

      {/* Price Update Rules */}
      {updateInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Price Update Rules
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">{updateInfo.description}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {updateInfo.rules.map((rule, index) => (
                <div key={index} className="border rounded-lg p-4 bg-gray-50">
                  <h4 className="font-semibold text-gray-900 mb-2">{rule.condition}</h4>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      {rule.increase}
                    </Badge>
                    <span className="text-sm text-gray-600">increase</span>
                  </div>
                  <p className="text-sm text-gray-500">Example: {rule.example}</p>
                </div>
              ))}
            </div>

            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {updateInfo.notes.map((note, index) => (
                    <li key={index} className="text-sm">{note}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Update Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Update Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="batchSize">Batch Size</Label>
              <Input
                id="batchSize"
                type="number"
                min="1"
                max="100"
                value={batchSize}
                onChange={(e) => setBatchSize(parseInt(e.target.value) || 50)}
                placeholder="50"
              />
              <p className="text-sm text-gray-500">
                Number of products to process at once (1-100)
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="dryRun"
                  checked={dryRun}
                  onCheckedChange={setDryRun}
                />
                <Label htmlFor="dryRun">Dry Run Mode</Label>
              </div>
              <p className="text-sm text-gray-500">
                {dryRun 
                  ? "Preview changes without applying them" 
                  : "Apply price changes to products"
                }
              </p>
            </div>
          </div>

          {!dryRun && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> This will permanently update product prices. 
                Consider running in dry run mode first to preview changes.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Action Button */}
      <Card>
        <CardContent className="pt-6">
          <Button
            onClick={handlePriceUpdate}
            disabled={isLoading}
            size="lg"
            className="w-full md:w-auto"
          >
            <Play className="h-4 w-4 mr-2" />
            {isLoading 
              ? "Starting Price Update..." 
              : `${dryRun ? "Preview" : "Apply"} Price Updates`
            }
          </Button>
        </CardContent>
      </Card>

      {/* Last Result */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {lastResult.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-red-600" />
              )}
              Update Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {lastResult.success ? (
              <div className="space-y-2">
                <p className="text-green-600 font-medium">{lastResult.message}</p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Process ID:</span>
                    <p className="font-mono">{lastResult.processId}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Batch Size:</span>
                    <p>{lastResult.batchSize}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Mode:</span>
                    <p>{lastResult.dryRun ? "Dry Run" : "Live Update"}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Rules:</span>
                    <p className="text-xs">
                      M1000-M1400: +M250<br/>
                      Others: +M300
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-red-600">{lastResult.error}</p>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
