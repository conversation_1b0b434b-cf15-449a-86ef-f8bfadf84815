"use server";

import prisma from "@/lib/prisma";


/**
 * Get dashboard statistics
 */
export async function getDashboardStats() {
  try {
    const [
      totalUsers,
      totalProducts,
      totalOrders,
      totalRevenue,
      totalProfit,
      pendingRevenue,
      pendingOrders,
      unreadMessages,
      recentOrders,
      recentUsers
    ] = await Promise.all([
      // Total users
      prisma.user.count(),
      
      // Total products
      prisma.product.count({ where: { isActive: true } }),
      
      // Total orders
      prisma.order.count(),
      
      // Total revenue (only orders with complete cost price data)
      prisma.order.aggregate({
        _sum: { totalAmount: true },
        where: {
          status: { not: "CANCELLED" },
          totalCostPrice: { not: null } // Only count orders where all items have cost prices
        }
      }),

      // Total profit (only orders with complete cost price data)
      prisma.order.aggregate({
        _sum: { totalProfit: true },
        where: {
          status: { not: "CANCELLED" },
          totalProfit: { not: null }
        }
      }),

      // Pending revenue (orders without complete cost price data)
      prisma.order.aggregate({
        _sum: { totalAmount: true },
        where: {
          status: { not: "CANCELLED" },
          totalCostPrice: null // Orders missing cost price data
        }
      }),

      // Pending orders
      prisma.order.count({ where: { status: "PENDING" } }),
      
      // Unread contact messages
      prisma.contactMessage.count({ where: { status: "UNREAD" } }),
      
      // Recent orders
      prisma.order.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              orderItems: true
            }
          }
        }
      }),
      
      // Recent users
      prisma.user.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true
        }
      })
    ]);

    return {
      success: true,
      data: {
        totalUsers,
        totalProducts,
        totalOrders,
        totalRevenue: totalRevenue._sum.totalAmount || 0,
        totalProfit: totalProfit._sum.totalProfit || 0,
        pendingRevenue: pendingRevenue._sum.totalAmount || 0,
        pendingOrders,
        unreadMessages,
        recentOrders: recentOrders.map(order => ({
          id: order.id,
          orderNumber: order.orderNumber,
          customerName: order.user.name,
          total: order.totalAmount,
          status: order.status,
          createdAt: order.createdAt.toISOString(),
          itemCount: order._count.orderItems
        })),
        recentUsers: recentUsers.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          createdAt: user.createdAt.toISOString()
        }))
      }
    };
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return { success: false, error: "Failed to fetch dashboard statistics" };
  }
}

/**
 * Get analytics data for admin analytics page
 */
export async function getAnalytics(timeRange: string = "30d") {
  try {
    // Calculate date range
    const now = new Date();
    let startDate = new Date();
    let previousStartDate = new Date();

    switch (timeRange) {
      case "7d":
        startDate.setDate(now.getDate() - 7);
        previousStartDate.setDate(now.getDate() - 14);
        break;
      case "30d":
        startDate.setDate(now.getDate() - 30);
        previousStartDate.setDate(now.getDate() - 60);
        break;
      case "90d":
        startDate.setDate(now.getDate() - 90);
        previousStartDate.setDate(now.getDate() - 180);
        break;
      case "1y":
        startDate.setFullYear(now.getFullYear() - 1);
        previousStartDate.setFullYear(now.getFullYear() - 2);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
        previousStartDate.setDate(now.getDate() - 60);
    }

    const [
      currentPeriodStats,
      previousPeriodStats,
      salesByMonth,
      topProducts,
      ordersByStatus,
      userGrowth
    ] = await Promise.all([
      // Current period stats
      Promise.all([
        prisma.order.aggregate({
          _sum: { totalAmount: true },
          _count: true,
          where: {
            createdAt: { gte: startDate },
            status: { not: "CANCELLED" }
          }
        }),
        prisma.user.count({
          where: { createdAt: { gte: startDate } }
        }),
        prisma.product.count({
          where: { createdAt: { gte: startDate } }
        })
      ]),
      
      // Previous period stats
      Promise.all([
        prisma.order.aggregate({
          _sum: { totalAmount: true },
          _count: true,
          where: {
            createdAt: { gte: previousStartDate, lt: startDate },
            status: { not: "CANCELLED" }
          }
        }),
        prisma.user.count({
          where: { createdAt: { gte: previousStartDate, lt: startDate } }
        }),
        prisma.product.count({
          where: { createdAt: { gte: previousStartDate, lt: startDate } }
        })
      ]),
      
      // Sales by month (last 6 months)
      prisma.$queryRaw`
        SELECT 
          TO_CHAR(DATE_TRUNC('month', "createdAt"), 'Mon') as month,
          SUM("totalAmount")::float as revenue,
          COUNT(*)::int as orders
        FROM "Order" 
        WHERE "createdAt" >= NOW() - INTERVAL '6 months'
          AND "status" != 'CANCELLED'
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY DATE_TRUNC('month', "createdAt")
      `,
      
      // Top products
      prisma.$queryRaw`
        SELECT 
          p.id,
          p.name,
          SUM(oi.quantity)::int as sales,
          SUM(oi.quantity * oi.price)::float as revenue
        FROM "Product" p
        JOIN "OrderItem" oi ON p.id = oi."productId"
        JOIN "Order" o ON oi."orderId" = o.id
        WHERE o."status" != 'CANCELLED'
          AND o."createdAt" >= NOW() - INTERVAL '30 days'
        GROUP BY p.id, p.name
        ORDER BY sales DESC
        LIMIT 5
      `,
      
      // Orders by status
      prisma.order.groupBy({
        by: ['status'],
        _count: true,
        where: {
          createdAt: { gte: startDate }
        }
      }),
      
      // User growth (last 6 months)
      prisma.$queryRaw`
        SELECT 
          TO_CHAR(DATE_TRUNC('month', "createdAt"), 'Mon') as month,
          COUNT(*)::int as "newUsers",
          (SELECT COUNT(*)::int FROM "User" WHERE "createdAt" <= DATE_TRUNC('month', u."createdAt") + INTERVAL '1 month' - INTERVAL '1 day') as "totalUsers"
        FROM "User" u
        WHERE "createdAt" >= NOW() - INTERVAL '6 months'
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY DATE_TRUNC('month', "createdAt")
      `
    ]);

    // Calculate growth percentages
    const currentRevenue = currentPeriodStats[0]._sum.totalAmount || 0;
    const previousRevenue = previousPeriodStats[0]._sum.totalAmount || 0;
    const revenueGrowth = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;

    const currentOrders = currentPeriodStats[0]._count;
    const previousOrders = previousPeriodStats[0]._count;
    const ordersGrowth = previousOrders > 0 ? ((currentOrders - previousOrders) / previousOrders) * 100 : 0;

    const currentUsers = currentPeriodStats[1];
    const previousUsers = previousPeriodStats[1];
    const usersGrowth = previousUsers > 0 ? ((currentUsers - previousUsers) / previousUsers) * 100 : 0;

    const currentProducts = currentPeriodStats[2];
    const previousProducts = previousPeriodStats[2];
    const productsGrowth = previousProducts > 0 ? ((currentProducts - previousProducts) / previousProducts) * 100 : 0;

    // Calculate order status percentages
    const totalOrdersInPeriod = ordersByStatus.reduce((sum, status) => sum + status._count, 0);
    const orderStatusData = ordersByStatus.map(status => ({
      status: status.status,
      count: status._count,
      percentage: totalOrdersInPeriod > 0 ? (status._count / totalOrdersInPeriod) * 100 : 0
    }));

    return {
      success: true,
      data: {
        revenue: {
          current: currentRevenue,
          previous: previousRevenue,
          growth: revenueGrowth
        },
        orders: {
          current: currentOrders,
          previous: previousOrders,
          growth: ordersGrowth
        },
        users: {
          current: currentUsers,
          previous: previousUsers,
          growth: usersGrowth
        },
        products: {
          current: currentProducts,
          previous: previousProducts,
          growth: productsGrowth
        },
        salesByMonth: salesByMonth as Array<{
          month: string;
          revenue: number;
          orders: number;
        }>,
        topProducts: topProducts as Array<{
          id: string;
          name: string;
          sales: number;
          revenue: number;
        }>,
        ordersByStatus: orderStatusData,
        userGrowth: userGrowth as Array<{
          month: string;
          newUsers: number;
          totalUsers: number;
        }>
      }
    };
  } catch (error) {
    console.error("Error fetching analytics:", error);
    return { success: false, error: "Failed to fetch analytics data" };
  }
}

/**
 * Get product performance analytics
 */
export async function getProductAnalytics() {
  try {
    const [
      topSellingProducts,
      lowStockProducts,
      categoryPerformance
    ] = await Promise.all([
      // Top selling products
      prisma.$queryRaw`
        SELECT 
          p.id,
          p.name,
          p.brand,
          p.price,
          SUM(oi.quantity)::int as "totalSold",
          SUM(oi.quantity * oi.price)::float as "totalRevenue"
        FROM "Product" p
        JOIN "OrderItem" oi ON p.id = oi."productId"
        JOIN "Order" o ON oi."orderId" = o.id
        WHERE o."status" != 'CANCELLED'
        GROUP BY p.id, p.name, p.brand, p.price
        ORDER BY "totalSold" DESC
        LIMIT 10
      `,
      
      // Low stock products
      prisma.product.findMany({
        where: {
          stock: { lte: 10 },
          isActive: true
        },
        select: {
          id: true,
          name: true,
          brand: true,
          stock: true,
          price: true
        },
        orderBy: { stock: "asc" }
      }),
      
      // Category performance
      prisma.$queryRaw`
        SELECT 
          c.id,
          c.name,
          COUNT(DISTINCT p.id)::int as "productCount",
          COALESCE(SUM(oi.quantity), 0)::int as "totalSold",
          COALESCE(SUM(oi.quantity * oi.price), 0)::float as "totalRevenue"
        FROM "Category" c
        LEFT JOIN "Product" p ON c.id = p."categoryId" AND p."isActive" = true
        LEFT JOIN "OrderItem" oi ON p.id = oi."productId"
        LEFT JOIN "Order" o ON oi."orderId" = o.id AND o."status" != 'CANCELLED'
        WHERE c."isActive" = true
        GROUP BY c.id, c.name
        ORDER BY "totalRevenue" DESC
      `
    ]);

    return {
      success: true,
      data: {
        topSellingProducts,
        lowStockProducts,
        categoryPerformance
      }
    };
  } catch (error) {
    console.error("Error fetching product analytics:", error);
    return { success: false, error: "Failed to fetch product analytics" };
  }
}
